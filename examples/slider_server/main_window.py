#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
from PyQt5.uic import loadUi
from PyQt5.QtCore import pyqtSlot
from PyQt5.QtGui import QIntValidator, QCloseEvent
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON>indow, QMessageBox
from hdmtv.core.node import Request, Response
from slider_server_node import SliderServerNode, ServerCodes


class MainWindow(QMainWindow):
    """ Server UI main window. """

    def __init__(self, parent=None) -> None:
        super(MainWindow, self).__init__(parent)
        loadUi(os.path.join(os.path.abspath(os.path.dirname(__file__)), "main_window.ui").replace("\n", ""), self)
        self._server: SliderServerNode = SliderServerNode()
        self.pos_line.setValidator(QIntValidator(self._server.device.min_value, self._server.device.max_value))
        self.pos_line.setText(str(self._server.device.slider_value))
        self.pos_label.setText(str(self._server.device.slider_value))
        self._server.cur_pos_signal.connect(self.render_position)

    @pyqtSlot()
    def run_request(self) -> None:
        """ Function for starting the slider bar. """
        response: Response = self._server.run_slider(request=Request())
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def inv_run_request(self) -> None:
        """ Function for starting the slider bar run inversely. """
        response: Response = self._server.inv_run_slider(request=Request())
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def goto_request(self) -> None:
        """ Function for starting the slider bar running towards a certain target. """
        if len(self.pos_line.text()) == 0:
            return self._prompt_alert(title="执行异常", message="请输入目标位置")
        response: Response = self._server.goto_position(
            request=Request(data={"position": int(self.pos_line.text())}))
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def home_request(self) -> None:
        """ Function for starting the slider bar running back home. """
        response: Response = self._server.home_reqeust(request=Request())
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def pause_request(self) -> None:
        """ Function for pausing the running. """
        response: Response = self._server.pause_reqeust(request=Request())
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot(int)
    def slider_moved_request(self, position: int) -> None:
        """ Function for starting the manual slider movement. """
        response: Response = self._server.manual_move_reqeust(request=Request(data={"position": position}))
        if response.code != ServerCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        else:
            self.pos_label.setText(str(position))

    @pyqtSlot()
    def exit_request(self) -> None:
        """ Function for exiting the current app. """
        self.close()

    @pyqtSlot(int)
    def render_position(self, position: int) -> None:
        """ Function for the slider position's rendering. """
        self.slider.setValue(position)
        self.pos_label.setText(str(position))

    def closeEvent(self, event: QCloseEvent) -> None:
        """ Overwritten the close event function. """
        super().closeEvent(event)
        self._server.exit()
        self._server.destroy_node()

    def _prompt_alert(self, title: str, message: str) -> None:
        """ Function for prompting the alert. """
        QMessageBox.warning(self,  title, message, QMessageBox.Yes)
