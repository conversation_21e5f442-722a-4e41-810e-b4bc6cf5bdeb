#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
from dataclasses import dataclass
from PyQt5.QtCore import Q<PERSON><PERSON>Application, QObject, pyqtSignal
from hdmtv.core.flags import Codes
from hdmtv.core.node import Request, Response
from hdmtv.node import Node, Publisher, Service, Timer


@dataclass
class Device(object):
    """ Fake device class. """
    min_value: int = 0
    max_value: int = 99
    slider_value: int = 0


class ServerCodes(Codes):
    SERVER_RUNNING: int = 100


class SliderServerNode(Node, QObject):
    """ Node for the slider server. """

    cur_pos_signal = pyqtSignal(int)

    def __init__(self, **options) -> None:
        Node.__init__(
            self,
            node_name="slider_server",
            namespace="slider",
            **options
        )
        QObject.__init__(self)

        self._device: Device = Device()
        self._is_running: bool = False
        self._heartbeat: bool = False
        self._publish_interval: float = 0.05
        self._prefix: str = "/localhost/" + self._namespace
        self._heartbeat_publisher: Publisher = self.create_publisher(
            topic=self._prefix + "/slider/heartbeat", val=False)
        self._pos_publisher: Publisher = self.create_publisher(
            topic=self._prefix + "/slider/position", val=self._device.slider_value)
        self._heartbeat_timer: Timer = self.create_timer(
            timer_period_sec=self._publish_interval, callback=self.publish_slider_value)
        self._pos_timer: Timer = self.create_timer(
            timer_period_sec=self._publish_interval, callback=self.publish_heartbeat)

        self._run_slider_service: Service = self.create_service(
            srv_name=self._prefix + "/slider/run", callback=self.run_slider)
        self._inv_run_slider_service: Service = self.create_service(
            srv_name=self._prefix + "/slider/inv_run", callback=self.inv_run_slider)
        self._goto_pos_service: Service = self.create_service(
            srv_name=self._prefix + "/slider/goto_pos", callback=self.goto_position)
        self._home_service: Service = self.create_service(
            srv_name=self._prefix + "/slider/home", callback=self.home_reqeust)
        self._pause_service: Service = self.create_service(
            srv_name=self._prefix + "/slider/pause", callback=self.pause_reqeust)

    @property
    def device(self) -> Device:
        """ Returns the current device object. """
        return self._device

    def publish_heartbeat(self) -> None:
        """ Publishing the device's heartbeat. """
        self._heartbeat = not self._heartbeat
        self._heartbeat_publisher.publish(msg=self._heartbeat)

    def publish_slider_value(self) -> None:
        """ Publishing the current slider's value. """
        self._pos_publisher.publish(msg=self._device.slider_value)

    def run_slider(self, request: Request) -> Response:
        """ Function for starting the slider bar. """
        if self._is_running:
            return Response(code=ServerCodes.SERVER_RUNNING, message="设备正在运行，请稍后...")
        self._run_slider(target=self._device.max_value)
        return Response(code=ServerCodes.SUCCESS, message="设备运行已完成")

    def inv_run_slider(self, request: Request) -> Response:
        """ Function for starting the slider bar run inversely. """
        if self._is_running:
            return Response(code=ServerCodes.SERVER_RUNNING, message="设备正在运行，请稍后...")
        self._run_slider(target=self._device.min_value)
        return Response(code=ServerCodes.SUCCESS, message="设备运行已完成")

    def goto_position(self, request: Request) -> Response:
        """ Function for starting the slider bar running towards a certain target. """
        if self._is_running:
            return Response(code=ServerCodes.SERVER_RUNNING, message="设备正在运行，请稍后...")
        position: int = request.data.get("position", self._device.slider_value)
        self._run_slider(target=position)
        return Response(code=ServerCodes.SUCCESS, message="设备运行已完成")

    def home_reqeust(self, request: Request) -> Response:
        """ Function for starting the slider bar running back home. """
        if self._is_running:
            return Response(code=ServerCodes.SERVER_RUNNING, message="设备正在运行，请稍后...")
        self._device.slider_value = 0
        self.cur_pos_signal.emit(self._device.slider_value)
        return Response(code=ServerCodes.SUCCESS, message="设备运行已完成")

    def manual_move_reqeust(self, request: Request) -> Response:
        """ Function for starting the slider bar's manually movement. """
        if self._is_running:
            return Response(code=ServerCodes.SERVER_RUNNING, message="设备正在运行，请稍后...")
        self._device.slider_value = request.data.get("position", self._device.slider_value)
        return Response(code=ServerCodes.SUCCESS, message="轨道数据已更新")

    def pause_reqeust(self, request: Request) -> Response:
        """ Function for starting the slider bar running back home. """
        self._is_running = False
        return Response(code=ServerCodes.SUCCESS, message="设备已停止")

    def exit(self) -> None:
        """ Exit the current running loop. """
        self._is_running = False

    def _run_slider(self, target: int, interval: float = 0.1) -> None:
        """ Function for running the slider into a certain target position. """
        if target < self._device.min_value or target > self._device.max_value:
            return None

        self._is_running = True
        cur_value: int = self._device.slider_value
        while cur_value != target and self._is_running:
            if cur_value > target:
                self._device.slider_value = cur_value - 1
            else:
                self._device.slider_value = cur_value + 1
            self.cur_pos_signal.emit(self._device.slider_value)
            cur_value: int = self._device.slider_value
            QCoreApplication.processEvents()
            time.sleep(interval)
        self._is_running = False




