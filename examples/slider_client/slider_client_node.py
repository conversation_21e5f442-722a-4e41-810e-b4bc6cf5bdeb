#!/usr/bin/python
# -*- coding: utf-8 -*-
import time

from PyQt5.QtCore import QThread, pyqtSignal
from hdmtv.core.flags import Codes
from hdmtv.core.node import Request, Response
from hdmtv.node import Node, Subscription, Client, Future


class ClientCodes(Codes):
    SERVER_RUNNING: int = 100


class SliderClientNode(Node, QThread):
    """ Node for the slider client. """

    cur_pos_signal = pyqtSignal(int)
    heartbeat_signal = pyqtSignal(bool)
    async_run_complete_signal = pyqtSignal(Response)

    def __init__(self, **options) -> None:
        Node.__init__(
            self,
            node_name="slider_client",
            namespace="slider",
            **options
        )
        QThread.__init__(self)
        self._prefix: str = "/localhost/" + self._namespace
        self._heartbeat_subscription: Subscription = self.create_subscription(
            topic=self._prefix + "/slider/heartbeat", callback=self.subscribe_heartbeat)
        self._pos_subscription: Subscription = self.create_subscription(
            topic=self._prefix + "/slider/position", callback=self.subscribe_slider_value)

        self._is_exit: bool = False
        self._loop_interval: float = 0.5
        self._disconnect_timeout: float = 3.0
        self._recent_connected_time: float = time.time()
        self._run_slider_client: Client = self.create_client(srv_name=self._prefix + "/slider/run")
        self._inv_run_slider_client: Client = self.create_client(srv_name=self._prefix + "/slider/inv_run")
        self._goto_pos_client: Client = self.create_client(srv_name=self._prefix + "/slider/goto_pos")
        self._home_client: Client = self.create_client(srv_name=self._prefix + "/slider/home")
        self._pause_client: Client = self.create_client(srv_name=self._prefix + "/slider/pause")

    def subscribe_heartbeat(self, heartbeat: bool) -> None:
        """ Subscription slot for the current slider's heartbeat. """
        # print("Received heartbeat: {}".format(heartbeat))
        self._recent_connected_time = time.time()

    def subscribe_slider_value(self, position: int) -> None:
        """ Subscription slot for the current slider's value. """
        self.cur_pos_signal.emit(position)

    def send_async_response(self, response: Response) -> None:
        """ Function for sending the asynchronous response. """
        self.async_run_complete_signal.emit(response)

    def run_slider(self) -> Response:
        """ Function for starting the slider bar. """
        future: Future = self._run_slider_client.call_async(request=Request())
        future.add_done_callback(callback=self.send_async_response)
        return Response(code=ClientCodes.SUCCESS, message="指令已发送")

    def inv_run_slider(self) -> Response:
        """ Function for starting the slider bar run inversely. """
        future: Future = self._inv_run_slider_client.call_async(request=Request())
        future.add_done_callback(callback=self.send_async_response)
        return Response(code=ClientCodes.SUCCESS, message="指令已发送")

    def goto_position(self, position: int) -> Response:
        """ Function for starting the slider bar running towards a certain target. """
        future: Future = self._goto_pos_client.call_async(request=Request(data={"position": position}))
        future.add_done_callback(callback=self.send_async_response)
        return Response(code=ClientCodes.SUCCESS, message="指令已发送")

    def home_reqeust(self) -> Response:
        """ Function for starting the slider bar running back home. """
        return self._home_client.call(request=Request())

    def manual_move_reqeust(self, position: int) -> Response:
        """ Function for starting the slider bar's manually movement. """
        return self._goto_pos_client.call(request=Request(data={"position": position}))

    def pause_reqeust(self) -> Response:
        """ Function for starting the slider bar running back home. """
        return self._pause_client.call(request=Request())

    def destroy(self) -> None:
        """ Destroy the current running loop. """
        self._is_exit = True

    def run(self) -> None:
        """ Main running loop for monitoring the connection status. """
        while  not self._is_exit:
            if time.time() - self._recent_connected_time >= self._disconnect_timeout:
                self.heartbeat_signal.emit(False)
            else:
                self.heartbeat_signal.emit(True)
            time.sleep(self._loop_interval)






