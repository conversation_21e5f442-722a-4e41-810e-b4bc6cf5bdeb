#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import typing as t
from PyQt5.uic import loadUi
from PyQt5.QtCore import pyqtSlot
from PyQt5.QtGui import QIntValidator, QCloseEvent
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON>indow, QMessageBox
from hdmtv.core.node import Response
from slider_client_node import SliderClientNode, ClientCodes


class MainWindow(QMainWindow):
    """ Client UI main window. """

    def __init__(self, parent=None) -> None:
        super(MainWindow, self).__init__(parent)
        slider_min_value: int = 0
        slider_max_value: int = 99

        loadUi(os.path.join(os.path.abspath(os.path.dirname(__file__)), "main_window.ui").replace("\n", ""), self)
        self._client: SliderClientNode = SliderClientNode()
        self._client.start()
        self.pos_line.setValidator(QIntValidator(slider_min_value, slider_max_value))
        self.pos_line.setText(str(slider_min_value))
        self.pos_label.setText(str(slider_min_value))
        self._client.cur_pos_signal.connect(self.render_position)
        self._client.heartbeat_signal.connect(self.render_connect_state)
        self._client.async_run_complete_signal.connect(self.render_async_response)

        self._connected_color: str = "background-color:rgb(0,255,0);"
        self._disconnected_color: str = "background-color:rgb(255,0,0);"
        self.state_label.setStyleSheet(self._disconnected_color)

    @pyqtSlot()
    def run_request(self) -> None:
        """ Function for starting the slider bar. """
        response: Response = self._client.run_slider()
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        self._prompt_waiting()

    @pyqtSlot()
    def inv_run_request(self) -> None:
        """ Function for starting the slider bar run inversely. """
        response: Response = self._client.inv_run_slider()
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        self._prompt_waiting()

    @pyqtSlot()
    def goto_request(self) -> None:
        """ Function for starting the slider bar running towards a certain target. """
        if len(self.pos_line.text()) == 0:
            return self._prompt_alert(title="执行异常", message="请输入目标位置")
        response: Response = self._client.goto_position(position=int(self.pos_line.text()))
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        self._prompt_waiting()

    @pyqtSlot()
    def home_request(self) -> None:
        """ Function for starting the slider bar running back home. """
        response: Response = self._client.home_reqeust()
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def pause_request(self) -> None:
        """ Function for pausing the running. """
        response: Response = self._client.pause_reqeust()
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)

    @pyqtSlot()
    def slider_moved_request(self) -> None:
        """ Function for starting the manual slider movement. """
        response: Response = self._client.goto_position(position=self.slider.value())
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        self._prompt_waiting()

    @pyqtSlot()
    def exit_request(self) -> None:
        """ Function for exiting the current app. """
        self.close()

    @pyqtSlot(int)
    def render_position(self, position: int) -> None:
        """ Function for the slider position's rendering. """
        self.slider.setValue(position)
        self.pos_label.setText(str(position))

    @pyqtSlot(Response)
    def render_async_response(self, response: Response) -> None:
        """ Function for rendering the asynchronous response. """
        if response.code != ClientCodes.SUCCESS:
            self._prompt_alert(title="执行异常", message=response.message)
        else:
            QMessageBox.information(self, "执行完成", "任务执行已完成", QMessageBox.Yes)

    @pyqtSlot(bool)
    def render_connect_state(self, connected: bool) -> None:
        """ Function for the slider position's rendering. """
        if connected:
            self.state_label.setStyleSheet(self._connected_color)
        else:
            self.state_label.setStyleSheet(self._disconnected_color)

    def closeEvent(self, event: QCloseEvent) -> None:
        """ Overwritten the close event function. """
        super().closeEvent(event)
        self._client.destroy()
        self._client.destroy_node()

    def _prompt_alert(self, title: str, message: str) -> None:
        """ Function for prompting the alert. """
        QMessageBox.warning(self,  title, message, QMessageBox.Yes)

    def _prompt_waiting(self, title: t.Optional[str] = None, message: t.Optional[str] = None) -> None:
        """ Function for prompting the waiting box. """
        pass
