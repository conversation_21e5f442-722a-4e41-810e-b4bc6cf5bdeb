#!/usr/bin/python
# -*- coding: utf-8 -*-

import sys
import hdmtv
from PyQt5.QtWidgets import QMainWindow, QApplication
from main_window import MainWindow


def main() -> None:
    """ Main running function. """
    hdmtv.init(log_path=None, config_file=None)

    app = QApplication(sys.argv)
    main_window: QMainWindow = MainWindow()
    hdmtv.launch()  # Launch all the registered nodes.
    main_window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()