<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>603</width>
    <height>300</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>603</width>
    <height>300</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>603</width>
    <height>300</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>客户端界面</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color:rgb(150,155,170);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QSlider" name="slider">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>150</y>
      <width>511</width>
      <height>22</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>AcadEref</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="orientation">
     <enum>Qt::Horizontal</enum>
    </property>
   </widget>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>220</y>
      <width>531</width>
      <height>41</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,1,1,1">
     <item>
      <widget class="QPushButton" name="run_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>正转</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="inv_run_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>反转</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pause_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>停止</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="home_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>回零</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="exit_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>退出</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>80</y>
      <width>551</width>
      <height>41</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,2,1,2,2,1">
     <property name="spacing">
      <number>10</number>
     </property>
     <item>
      <widget class="QLabel" name="state_label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>25</width>
         <height>25</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>25</width>
         <height>25</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(255,0,0);</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(150,155,170);</string>
       </property>
       <property name="text">
        <string>轨道当前位置</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="pos_label">
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(150,155,170);border: 1px solid #000000; </string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(150,155,170);</string>
       </property>
       <property name="text">
        <string>目标位置</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="pos_line">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(255,255,255);border: 1px solid #000000; </string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="goto_button">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="font">
        <font>
         <family>Arial</family>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color:rgb(160,155,170);</string>
       </property>
       <property name="text">
        <string>GO</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QLabel" name="label_3">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>10</y>
      <width>221</width>
      <height>39</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="font">
     <font>
      <family>Arial</family>
      <pointsize>16</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color:rgb(150,155,170);</string>
    </property>
    <property name="text">
     <string>轨道控制器客户端</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>run_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>run_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>131</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>143</x>
     <y>287</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>inv_run_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>inv_run_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>238</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>238</x>
     <y>420</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>goto_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>goto_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>559</x>
     <y>81</y>
    </hint>
    <hint type="destinationlabel">
     <x>532</x>
     <y>71</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pause_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>pause_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>345</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>372</x>
     <y>370</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>home_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>home_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>452</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>435</x>
     <y>391</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>exit_button</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>exit_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>559</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>549</x>
     <y>330</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>slider</sender>
   <signal>sliderReleased()</signal>
   <receiver>MainWindow</receiver>
   <slot>slider_moved_request()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>531</x>
     <y>153</y>
    </hint>
    <hint type="destinationlabel">
     <x>575</x>
     <y>158</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>run_request()</slot>
  <slot>inv_run_request()</slot>
  <slot>pause_request()</slot>
  <slot>home_request()</slot>
  <slot>goto_request()</slot>
  <slot>exit_request()</slot>
  <slot>slider_moved_request()</slot>
 </slots>
</ui>
