import socket
import msgpack
from typing import Any
import typing as t

class RPCError(Exception):
    """RPC调用异常基类"""
    pass

class RPCClient:
    def __init__(self, host: str = 'localhost', port: int = 9999):
        self.host = host
        self.port = port

    def __getattr__(self, name: str) -> t.Callable:
        """动态生成远程调用方法"""
        def remote_method(*args, **kwargs) -> Any:
            # 创建新连接
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                try:
                    sock.connect((self.host, self.port))
                except ConnectionRefusedError:
                    raise RPCError("无法连接到服务端")

                # 1. 准备请求数据
                request = {
                    'func': name,
                    'args': args,
                    'kwargs': kwargs
                }
                request_data = msgpack.packb(request, use_bin_type=True)

                # 2. 发送请求（带4字节长度头）
                header = len(request_data).to_bytes(4, 'big')
                sock.sendall(header + request_data)

                # 3. 接收响应头
                header = sock.recv(4)
                if not header:
                    raise RPCError("未收到响应头")

                data_len = int.from_bytes(header, 'big')

                # 4. 接收完整响应数据
                data = bytearray()
                while len(data) < data_len:
                    remaining = data_len - len(data)
                    chunk = sock.recv(min(4096, remaining))
                    if not chunk:
                        raise RPCError("响应数据不完整")
                    data.extend(chunk)

                # 5. 处理响应
                response = msgpack.unpackb(data, raw=False)
                # if response.get("code") == 0:
                #     return response
                # else:
                #     raise RPCError(response)
                print(type(response))

        return remote_method


if __name__ == "__main__":
    import time
    import typing as t
    ip_addr: str = "127.0.0.1"
    port: int = 4851
    client = RPCClient(host=ip_addr, port=port)
    try:
        num: int = 100
        start_time: float = time.time()
        total_time: float = 0.0
        for i in range(num):
            request: t.Dict[str, t.Any] = {"request": "Client"}
            response: t.Dict[str, t.Any] = client.ns_say_hello(request)  # 输出 5
            # print("Received from server: {}".format(response))
            cur_time = time.time()
            total_time += cur_time - start_time
            start_time = cur_time
        print("The average running time is {} seconds.".format(round(total_time / num, 6)))
    except Exception as e:
        print("Error:", e)