# client_async.py
import time

import zerorpc
import gevent
from threading import Thread
from queue import Queue
from gevent.event import AsyncResult


class AsyncZeroRPCClient:
    def __init__(self, endpoint):
        self.client = zerorpc.Client()
        self.client.connect(endpoint)

    def call_async(self, method_name, *args, **kwargs):
        """异步调用：返回 AsyncResult（类似 Future 的对象）"""
        async_result = AsyncResult()

        def _task():
            try:
                method = getattr(self.client, method_name)
                result = method(*args, **kwargs)
                async_result.set(result)
            except Exception as e:
                async_result.set_exception(e)

        gevent.spawn(_task)  # 启动协程执行任务
        return async_result


class AsyncThread(Thread):
    def __init__(self, endpoint):
        super().__init__()
        self.client = None
        self.endpoint = endpoint
        self.queue = Queue()

    def call_async(self, method_name, *args, **kwargs):
        """异步调用：返回 AsyncResult（类似 Future 的对象）"""
        async_result = AsyncResult()

        def _task():
            try:
                method = getattr(self.client, method_name)
                result = method(*args, **kwargs)
                async_result.set(result)
            except Exception as e:
                async_result.set_exception(e)

        gevent.spawn(_task)  # 启动协程执行任务
        return async_result

    def run(self):
        self.client = zerorpc.Client()
        self.client.connect(self.endpoint)

        while True:
            data: Data = self.queue.get()
            print("Get the data object {}".format(data.method_name))
            future = client.call_async(data.callback, data.kwargs)
            future.rawlink(data.callback)  # 使用 rawlink 添加回调



if __name__ == "__main__":
    import typing as t
    from dataclasses import dataclass
    client = AsyncThread("tcp://127.0.0.1:4851")
    client.start()

    @dataclass
    class Data:
        callback: t.Callable
        method_name: str
        kwargs: t.Dict[str, t.Any]

    # 注册回调
    def callback(async_result):
        try:
            print(f"回调结果: {async_result.get()}")
        except Exception as e:
            print(f"回调异常: {e}")


    # future = client.call_async("say_hello", {"a": 2})
    # future.rawlink(callback)  # 使用 rawlink 添加回调

    print("主协程继续执行其他任务...")

    inp_data: Data = Data(callback=callback, method_name="say_hello", kwargs={"a": 2})
    client.queue.put(inp_data)

    # 保持主协程运行（模拟其他逻辑）
    # gevent.sleep(5)
