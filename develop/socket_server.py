import socket
import threading
import msgpack
from typing import Dict, Callable


class RPCServer:
    def __init__(self, host: str = 'localhost', port: int = 9999):
        self.host = host
        self.port = port
        self.funcs: Dict[str, Callable] = {}
        self.running = False
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    def register_function(self, func: Callable, name: str = None) -> None:
        """注册可供远程调用的函数"""
        name = name or func.__name__
        self.funcs[name] = func

    def _handle_connection(self, client_socket: socket.socket) -> None:
        """处理客户端连接"""
        try:
            # 1. 读取数据头（4字节长度）
            header = client_socket.recv(4)
            if not header:
                return
            data_len = int.from_bytes(header, byteorder='big')

            # 2. 读取完整数据
            data = bytearray()
            while len(data) < data_len:
                remaining = data_len - len(data)
                chunk = client_socket.recv(min(4096, remaining))
                if not chunk:
                    raise ConnectionError("Incomplete data")
                data.extend(chunk)

            # 3. 反序列化并处理请求
            request = msgpack.unpackb(data, raw=False)
            func_name = request['func']
            args = request.get('args', [])
            kwargs = request.get('kwargs', {})

            if func_name not in self.funcs:
                response = {'status': 'error', 'result': f'Function {func_name} not found'}
            else:
                try:
                    result = self.funcs[func_name](*args, **kwargs)
                    response = {'status': 'ok', 'result': result}
                except Exception as e:
                    response = {'status': 'error', 'result': str(e)}

            # 4. 发送响应
            response_data = msgpack.packb(response, use_bin_type=True)
            header = len(response_data).to_bytes(4, 'big')
            client_socket.sendall(header + response_data)

        except Exception as e:
            print(f"处理请求时发生错误: {str(e)}")
        finally:
            client_socket.close()

    def start(self) -> None:
        """启动RPC服务端"""
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        self.running = True
        print(f"RPC Server 启动于 {self.host}:{self.port}")

        try:
            while self.running:
                client_socket, addr = self.server_socket.accept()
                print(f"接受来自 {addr} 的连接")
                thread = threading.Thread(target=self._handle_connection, args=(client_socket,))
                thread.start()
        except KeyboardInterrupt:
            self.stop()

    def stop(self) -> None:
        """停止服务端"""
        self.running = False
        self.server_socket.close()
        print("服务端已停止")



if __name__ == "__main__":
    ip_addr: str = "127.0.0.1"
    port: int = 4242
    import typing as t
    # 示例函数

    class MyClass:
        name: str = "Server"
        def function(self, request: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
            print("Received from client: {}".format(request))
            return {"response": "Received from {}, and hey there from {}".format(
                request.get("request"), self.name)}


    # 启动服务端
    my_class = MyClass()
    server = RPCServer(ip_addr, port)
    server.register_function(my_class.function)  # 注册函数
    print("Server running on {}:{}...".format(ip_addr, port))
    server.start()
