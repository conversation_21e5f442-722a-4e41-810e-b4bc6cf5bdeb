import asyncio
import logging
from asyncua import Client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("OPCUA-Client")


class SubscriptionHandler:
    def datachange_notification(self, node, val, data):
        logger.info(f"Data updated: {val}")


class ResilientClient(object):
    def __init__(self, server_url, node_id):
        self.server_url = server_url
        self.node_id = node_id  # 订阅的目标变量 NodeID
        self.client = None
        self.subscription = None
        self.handler = SubscriptionHandler()
        self.retry_interval = 5  # 重连间隔（秒）

    async def _setup_subscription(self):
        """创建订阅并监控目标变量"""
        try:
            self.client = Client(url=self.server_url)
            await self.client.connect()
            logger.info(f"Connected to {self.server_url}")

            # 获取目标节点
            node = self.client.get_node(self.node_id)
            logger.info(f"Target node: {await node.read_browse_name()}")

            # 创建新订阅
            self.subscription = await self.client.create_subscription(
                500,  # 发布间隔
                self.handler
            )
            await self.subscription.subscribe_data_change(node)
            return True

        except Exception as e:
            logger.error(f"Setup failed: {str(e)}")
            await self._cleanup()
            return False

    async def _cleanup(self):
        """清理残留资源"""
        try:
            if self.subscription:
                await self.subscription.delete()
                self.subscription = None
            if self.client:
                await self.client.disconnect()
                self.client = None
        except Exception as expt:
            print(expt)

    async def run(self):
        """带自动重连的主循环"""
        while True:
            try:
                success = await self._setup_subscription()
                if not success:
                    raise ConnectionError("Initial connection failed")

                # 保持连接直至检测到断开
                while True:
                    await asyncio.sleep(1.0)  # 健康检查间隔
                    await self.client.check_connection()

            except (ConnectionError, ConnectionAbortedError) as e:
                logger.warning(f"Connection error: {str(e)}, retrying in {self.retry_interval}s...")
                await self._cleanup()
                await asyncio.sleep(self.retry_interval)

            except asyncio.CancelledError:
                logger.info("Client shutdown requested")
                await self._cleanup()
                await asyncio.sleep(self.retry_interval)

            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                await self._cleanup()
                await asyncio.sleep(self.retry_interval)


if __name__ == "__main__":
    # 配置参数
    SERVER_URL = "opc.tcp://127.0.0.1:4840/freeopcua/server/"
    TARGET_NODE_ID = "ns=2;s=my_var"  # 替换为实际 NodeID

    resilient_client = ResilientClient(SERVER_URL, TARGET_NODE_ID)

    try:
        asyncio.run(resilient_client.run())
    except KeyboardInterrupt:
        logger.info("Client stopped by user")