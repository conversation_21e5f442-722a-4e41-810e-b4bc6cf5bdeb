import asyncio
import zmq
import zmq.asyncio
import uuid
import msgpack
from typing import Dict, Optional
import sys


class AsyncZmqClient:
    def __init__(self, server_addr: str = "tcp://127.0.0.1:4851"):
        # Windows 事件循环强制兼容处理
        if sys.platform == "win32":
            # 优先使用 SelectorEventLoop（ZeroMQ 原生兼容）
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        self.context = zmq.asyncio.Context()
        self.socket = self.context.socket(zmq.REQ)
        self.socket.connect(server_addr)
        self.pending_futures: Dict[str, asyncio.Future] = {}  # 请求ID -> Future

    async def _response_listener(self):
        """异步监听服务器响应，并关联到对应的 Future"""
        while True:
            try:
                # 直接 await 异步接收（ZeroMQ 保证返回协程）
                raw_response = await self.socket.recv()
                response = msgpack.unpackb(raw_response, raw=False)
                request_id = response["request_id"]
                result = response["result"]

                if request_id in self.pending_futures:
                    future = self.pending_futures.pop(request_id)
                    if not future.done():
                        future.set_result(result)
            except asyncio.CancelledError:
                break  # 正常退出监听循环
            except msgpack.UnpackException as e:
                print(f"MsgPack 反序列化失败: {e}")
            except Exception as e:
                print(f"响应处理异常: {e}")

    def call_method(self, method: str, *args) -> asyncio.Future:
        """异步调用方法，立即返回 Future 对象"""
        request_id = str(uuid.uuid4())
        future = asyncio.get_running_loop().create_future()

        # 序列化请求为 MsgPack 二进制
        request_data = msgpack.packb({
            "request_id": request_id,
            "method": method,
            "args": args
        }, use_bin_type=True)

        # 关键修正：通过 asyncio.shield 确保协程正确传递
        async def send_coroutine():
            try:
                # 直接 await ZeroMQ 的 send 协程（确保是协程类型）
                await self.socket.send(request_data)
            except Exception as e:
                # 发送失败时，将异常传递给主 Future
                if not future.done():
                    future.set_exception(e)
                    await self.pending_futures.pop(request_id, None)

        # 创建任务运行发送协程（此时 send_coroutine 是协程对象）
        asyncio.create_task(send_coroutine())

        # 注册 Future 并设置超时
        self.pending_futures[request_id] = future
        self._set_future_timeout(future, request_id)
        return future

    def _set_future_timeout(self, future: asyncio.Future, request_id: str, timeout: float = 10.0):
        """为 Future 设置超时"""

        async def timeout_handler():
            await asyncio.sleep(timeout)
            if not future.done():
                future.set_exception(TimeoutError(f"请求 {request_id} 超时（{timeout}s）"))
                await self.pending_futures.pop(request_id, None)

        asyncio.create_task(timeout_handler())

    async def close(self):
        """优雅关闭客户端"""
        for future in self.pending_futures.values():
            if not future.done():
                future.cancel()
        self.pending_futures.clear()
        self.socket.close()
        self.context.term()


# 使用示例
async def main():
    client = AsyncZmqClient()
    listener_task = asyncio.create_task(client._response_listener())

    future = client.call_method("say_hello", {"param1": 42})

    def on_done(f: asyncio.Future):
        try:
            print(f"结果: {f.result()}")
        except Exception as e:
            print(f"错误: {e}")

    future.add_done_callback(on_done)

    # 等待足够时间接收响应（根据业务调整）
    await asyncio.sleep(5)

    await client.close()
    listener_task.cancel()


if __name__ == "__main__":
    asyncio.run(main())
