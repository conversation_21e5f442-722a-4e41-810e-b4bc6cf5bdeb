import asyncio
import os
import logging
import sqlite3  # 用于验证sqlite3功能
from asyncua import Server, ua
from asyncua.server.history_sql import HistorySQLite

# 启用DEBUG级别日志（关键！查看asyncua内部操作）
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_sqlite_functionality(db_path):
    """测试sqlite3模块是否能正常创建数据库"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, data TEXT)")
        cursor.execute("INSERT INTO test (data) VALUES ('test_data')")
        conn.commit()
        conn.close()
        logger.debug(f"[测试] SQLite模块正常，测试数据库已生成：{db_path}")
        return True
    except Exception as e:
        logger.error(f"[测试] SQLite模块异常：{e}")
        return False


async def update_node_value(node):
    """定时更新节点值（通过OPC UA服务修改）"""
    count = 0
    while True:
        try:
            new_value = f"消息_{count}"
            # 使用write_value_by_service模拟客户端通过OPC UA服务修改值（关键！）
            await node.write_value_by_service(new_value)
            logger.debug(f"[更新] 通过服务修改节点值为：{new_value}")
            count += 1
        except Exception as e:
            logger.error(f"[错误] 更新节点值失败：{e}")
        await asyncio.sleep(5)


async def main():
    try:
        # 1. 验证SQLite模块功能（关键排查步骤）
        test_db_path = os.path.join(os.getcwd(), "sqlite_test.db")
        sqlite_ok = await test_sqlite_functionality(test_db_path)
        if not sqlite_ok:
            logger.error("SQLite模块异常，无法创建数据库！")
            return

        # 2. 初始化OPC UA服务器
        server = Server()
        await server.init()
        server.set_endpoint("opc.tcp://127.0.0.1:4840/server/")
        server.set_server_name("AsyncUA SQLite History Server")

        # 3. 配置SQLite历史存储（使用绝对路径）
        current_dir = os.path.abspath(os.getcwd())
        db_dir = os.path.join(current_dir, "opcua_history")
        os.makedirs(db_dir, exist_ok=True)
        history_db_path = os.path.join(db_dir, "opcua_history.db")
        logger.debug(f"[配置] 历史数据库路径：{history_db_path}")

        # 绑定历史存储
        history_storage = HistorySQLite(path=history_db_path)
        server.iserver.history_manager.set_storage(history_storage)
        logger.debug(f"[验证] 历史存储绑定对象：{server.iserver.history_manager.storage}")

        # 4. 创建变量节点并启用历史存储
        ns_idx = await server.register_namespace("http://examples.freeopcua.github.io")
        objects = server.get_objects_node()
        my_var = await objects.add_variable(ns_idx, "MyMessage", "初始消息")
        await my_var.set_writable()  # 允许客户端修改

        # 启用Historizing属性（必须！）
        await my_var.write_attribute(ua.AttributeIds.Historizing, ua.DataValue(True))
        attr_value = await my_var.read_attribute(ua.AttributeIds.Historizing)
        logger.debug(f"[验证] Historizing属性值：{attr_value.Value.Value}（应为True）")

        # 5. 主动写入一条历史数据（强制触发存储）
        # 构造历史数据条目（时间戳、值）
        now = ua.get_current_time()
        history_data = [ua.HistoryData(
            DataValues=[ua.DataValue(Value=ua.Variant("初始历史消息"), SourceTimestamp=now)]
        )]
        # 调用历史管理器写入接口（核心！）
        await server.iserver.history_manager.write_history_data(
            my_var.nodeid, history_data
        )
        logger.debug("[操作] 已主动写入一条历史数据到数据库")

        # 6. 启动服务器并运行定时任务（通过服务修改值）
        async with server:
            logger.debug("[状态] 服务器已启动，开始通过服务更新节点值...")
            update_task = asyncio.create_task(update_node_value(my_var))
            await update_task  # 保持任务运行

    except Exception as e:
        logger.error(f"[错误] 脚本运行异常：{e}")


if __name__ == "__main__":
    asyncio.run(main())
