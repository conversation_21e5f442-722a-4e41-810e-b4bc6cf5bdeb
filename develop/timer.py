from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor

# 创建调度器，默认使用 ThreadPoolExecutor 线程池（最大10线程）
scheduler = BackgroundScheduler(
    executors={'default': ThreadPoolExecutor(10)}
)

def job():
    print("任务执行中...")

# 添加一个每5秒执行的任务
scheduler.add_job(job, 'interval', seconds=1)
scheduler.start()


if __name__ == "__main__":
    import time
    for i in range(5):
        time.sleep(1.0)
    scheduler.shutdown()