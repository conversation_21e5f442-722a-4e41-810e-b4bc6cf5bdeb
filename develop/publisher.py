import asyncio
from asyncua import Server
from asyncua.common.structures import StructGenerator

async def main():
    server = Server()
    await server.init()
    server.set_endpoint("opc.tcp://127.0.0.1:4840")

    # 设置命名空间
    uri = "example-uri"
    idx = await server.register_namespace(uri)

    # 创建对象节点
    myobj_orig = await server.nodes.objects.add_object("ns=2;s=my_object_orig", "MyObjectOrig")
    myobj = await myobj_orig.add_object("ns=2;s=my_object", "MyObject")

    # 创建变量节点（将被订阅的变量）
    var = await myobj.add_variable("ns=2;s=my_var", "MyVariable", 0.0)
    await var.set_writable(writable=False)  # 允许客户端写入

    # 启动服务器
    async with server:
        print("Server started at {}".format(server.endpoint))

        # 持续更新变量值（模拟数据变化）
        count = 0
        while True:
            await asyncio.sleep(1)
            count += 0.1
            # await var.write_value(count)
            await var.write_value(count)
            print("Updated value to:", count)

if __name__ == "__main__":
    asyncio.run(main())