#!/usr/bin/python
# -*- coding: utf-8 -*-

import asyncio
import typing as t
from loguru import logger
from hdmtv.core.executor import BaseExecutor
from hdmtv.node import Node, Client, Service, Publisher, Subscription, Timer
from hdmtv.core.cdi import get_instance
from hdmtv.recorder import DataRecorder


class Executor(BaseExecutor):
    """ Threadpool executor class defined for the node's execution. """

    def __init__(self, **options) -> None:
        super().__init__(**options)
        self._nodes: t.Set[Node] = set()
        self._is_exit: bool = False

    def add_node(self, node: Node, **options) -> bool:
        """ Add a node whose callbacks should be managed by this executor. """
        self._nodes.add(node)
        return True

    def remove_node(self, node: Node, **options) -> None:
        """ Stop managing this node's callbacks. """
        self._nodes.remove(node)

    def spin(self, **options) -> None:
        """ Execute callbacks until shutdown. """
        asyncio.run(self._spin_function(interval=options.get("interval", 1.0)))

    def launch(self, **options) -> bool:
        """ Launch the current registered nodes. """
        try:
            Timer.start_timer()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def shutdown(self, timeout_sec: float = None, **options) -> bool:
        """ Stop executing callbacks and wait for their completion. """
        is_shutdown: bool = True
        self._is_exit = True
        
        try:
            # 停止recorder
            try:
                recorder = get_instance(clazz=DataRecorder)
                recorder.stop_recording()
            except:
                pass  # recorder可能未初始化
            
            # 销毁所有节点
            for node in self._nodes:
                node.destroy_node()
        except Exception as expt:
            logger.error(str(expt))
            is_shutdown = False
        return is_shutdown

    async def _spin_function(self, interval: float = 1.0) -> None:
        """ Utility function for the spinning. """
        while not self._is_exit:
            await asyncio.sleep(interval)