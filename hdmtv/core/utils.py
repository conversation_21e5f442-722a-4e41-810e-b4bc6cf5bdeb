#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import json
import typing as t
from loguru import logger


def load_json(json_file: str) -> t.Dict[str, t.Any]:
    """  Load the json file into python dictionary. """
    with open(json_file.replace("\n", ""), "r", encoding="utf-8") as f:
        dictionary = json.load(f)
        f.close()
    return dictionary


def dump_json(json_file: str, dictionary: t.Dict[str, t.Any]) -> None:
    """ Dump the python dictionary object into json file. """
    with open(json_file, "w", encoding="utf-8") as f:
        json_obj = json.dumps(dictionary, indent=4)
        f.write(json_obj)
        f.close()


def load_json_file(json_file: str) -> t.Optional[t.Dict[str, t.Any]]:
    """ Load the json file, it returns None if error occurs. """
    json_file = json_file.replace("\n", "")
    if not os.path.isfile(json_file):
        logger.warning(f"Warning: Could not find json file {json_file}")
        return None
    try:
        dictionary: t.Optional[t.Dict[str, t.Any]] = load_json(json_file)
    except Exception:
        logger.exception("load json error")
        dictionary: t.Optional[t.Dict[str, t.Any]] = None
    return dictionary


def dump_json_file(json_file: str, dictionary: t.Dict[str, t.Any]) -> bool:
    """ dump the json file, it returns whether the dumping method is succeeded or not. """
    try:
        dump_json(json_file, dictionary)
        is_dumped: bool = True
    except Exception:
        logger.exception("dump json error")
        is_dumped: bool = False
    return is_dumped