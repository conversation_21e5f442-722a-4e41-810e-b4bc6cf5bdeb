#!/usr/bin/python
# -*- coding: utf-8 -*-

import injector
from injector import Injector, Module
from typing import Type, Union, Callable, List
from hdmtv.core.types import T

_g_cdi: Injector = Injector()


def install_modules(modules: Union[Module, List[Module]]) -> None:
    if isinstance(modules, list) is False:
        modules = [modules]    # type: ignore
    for module in modules:     # type: ignore
        _g_cdi.binder.install(module)


def binding(interface: Type[T],
            to: Union[None, T, Callable[..., T], injector.Provider[T]] = None,
            scope: Union[None, Type[injector.Scope], injector.ScopeDecorator] = injector.singleton) -> None:
    _g_cdi.binder.bind(interface, to, scope)


def get_instance(clazz: Type[T]) -> T:
    return _g_cdi.get(clazz)
