#!/usr/bin/python
# -*- coding: utf-8 -*-

import sys
import logging
import typing as t
from loguru import logger
from loguru_config import LoguruConfig


class LoggingHandler(logging.Handler):
    """ Logger handler definition. """

    def emit(self, record: logging.LogRecord) -> None:
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


def __handle_unhandled_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logger.opt(exception=exc_value).error("Uncaught exception")


def init_log(config_path: str) -> None:
    """ Main function for initialize the logger. """
    config_file = "{}/log.yaml".format(config_path)
    LoguruConfig.load(config_file, configure=True)
    logging.getLogger().addHandler(LoggingHandler())
    sys.excepthook = __handle_unhandled_exception


class Logger(object):
    """ Base logger class. """

    def __init__(self, **options) -> None:
        self._options = options

    def debug(self, message: str, **options) -> None:
        """ Logging the debug message. """
        logger.debug(self._message_process(message, **options))

    def info(self, message: str, **options) -> None:
        """ Logging the information. """
        logger.debug(self._message_process(message, **options))

    def warning(self, message: str, **options) -> None:
        """ Logging the warning. """
        logger.warning(self._message_process(message, **options))

    def warn(self, message: str, **options) -> None:
        """ Logging the warning. """
        logger.warning(self._message_process(message, **options))

    def error(self, message: str, **options) -> None:
        """ Logging the error message. """
        logger.error(self._message_process(message, **options))

    def fatal(self, message: str, **options) -> None:
        """ Logging the fatal error message. """
        logger.error(self._message_process(message, **options))

    @staticmethod
    def _message_process(message: str, **options) -> str:
        """ Function for message processing """
        name: t.Optional[str] = options.get("name")
        if name:
            message = "name: " + message
        return message
