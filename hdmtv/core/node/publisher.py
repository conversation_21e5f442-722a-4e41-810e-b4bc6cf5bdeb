#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from typing import TypeVar
from abc import abstractmethod

MsgType = TypeVar('MsgType')


class BasePublisher(object):
    """ Base publisher defining the publishing used by node. """

    def __init__(self, topic: str, msg_type: t.Optional[MsgType] = None, **options) -> None:
        self._msg_type = msg_type
        self._topic = topic
        self._options = options

    @property
    def topic_name(self) -> str:
        """ Returns the topic name. """
        return self._topic

    @abstractmethod
    def publish(self, msg: t.Any, **options) -> None:
        """ Send a message to the topic for the publisher. """
        pass

    @abstractmethod
    def destroy(self, **options) -> None:
        """ Function for destroying the publisher. """
        pass