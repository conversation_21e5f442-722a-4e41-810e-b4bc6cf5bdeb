#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from collections import OrderedDict


class ServiceData(OrderedDict):
    """ Service data class for the data exchange. """

    def __init__(self, **options) -> None:
        super().__init__()
        for key in options:
            self[key] = options[options[key]]

    def set(self, key: str, value: t.Any) -> None:
        """ Function for setting the service data. """
        self[key] = value


class Request(ServiceData):
    """ Request class for the data exchange. """

    def __init__(
        self,
        header: t.Optional[t.Dict[str, t.Any]] = None,
        data: t.Optional[t.Dict[str, t.Any]] = None,
        **options
    ) -> None:
        self.set("header", header or dict())
        self.set("data", data or dict())
        super().__init__(**options)

    @property
    def header(self) -> t.Dict[str, t.Any]:
        """ Returns the request's header. """
        return self.get("header", dict())

    @property
    def data(self) -> t.Dict[str, t.Any]:
        """ Returns the request's data. """
        return self.get("data", dict())

    @header.setter
    def header(self, header: t.Dict[str, t.Any]) -> None:
        """ Function for setting the header's value. """
        self.set("header", header or dict())

    @data.setter
    def data(self, data: t.Dict[str, t.Any]) -> None:
        """ Function for setting the data's value. """
        self.set("data", data or dict())


class Response(ServiceData):
    """ Response class for the data exchange. """

    def __init__(
        self,
        code: int = -1,
        message: str = "",
        data: t.Optional[t.Dict[str, t.Any]] = None,
        **options
    ) -> None:
        self.set("code", code)
        self.set("message", message or "")
        self.set("data", data or dict())
        super().__init__(**options)

    @property
    def code(self) -> int:
        """ Returns the response's code. """
        return self.get("code", -1)

    @property
    def message(self) -> str:
        """ Returns the response's message. """
        return self.get("message", "")

    @property
    def data(self) -> t.Dict[str, t.Any]:
        """ Returns the response's data. """
        return self.get("data", dict())

    @code.setter
    def code(self, code: int) -> None:
        """ Function for setting the code's value. """
        self.set("code", code or -1)

    @message.setter
    def message(self, message: str) -> None:
        """ Function for setting the message's value. """
        self.set("message", message or "")

    @data.setter
    def data(self, data: t.Dict[str, t.Any]) -> None:
        """ Function for setting the data's value. """
        self.set("data", data or dict())
