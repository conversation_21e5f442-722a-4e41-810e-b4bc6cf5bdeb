#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod
from .data import Request, Response
from .task import BaseFuture


class BaseClient(object):
    """ Base client class. """

    def __init__(self, srv_name: str, **options) -> None:
        self._srv_name = srv_name
        self._options = options

    @abstractmethod
    def call(self, request: Request, **options) -> Response:
        """ Function for performing the RPC procedure. """
        pass

    @abstractmethod
    def call_async(self, request: t.Dict[str, t.Any], **options) -> BaseFuture:
        """ Function for performing the asynchronous call procedure. """
        pass

    @abstractmethod
    def destroy(self, **options) -> None:
        """ Destroy the current client. """
        pass