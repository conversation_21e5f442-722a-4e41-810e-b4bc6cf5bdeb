#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod
from .data import Response


class BaseService(object):
    """ Base service server class. """

    def __init__(self, srv_name: str, callback: t.Callable, **options) -> None:
        self._srv_name = srv_name
        self._callback = callback
        self._options = options

    @abstractmethod
    def send_response(self, response: Response, **options) -> None:
        """ Function for sending the response. """
        pass

    @abstractmethod
    def destroy(self, **options) -> None:
        """ Destroy the current service server. """
        pass