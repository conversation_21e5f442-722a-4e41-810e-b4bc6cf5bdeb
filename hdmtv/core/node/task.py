#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod
from hdmtv.core.node.data import Response


class BaseFuture(object):
    """ Future class for the data exchange. """

    def __init__(self, **options) -> None:
        self._options = options

    @abstractmethod
    def result(self, **options) -> t.Optional[Response]:
        """ Returns the callback result. """
        pass

    @abstractmethod
    def done(self, **options) -> bool:
        """ Returns whether the task has been done. """
        pass

    @abstractmethod
    def cancelled(self, **options) -> bool:
        """ Returns whether the task has been cancelled. """
        pass

    @abstractmethod
    def cancel(self, **options) -> bool:
        """ Cancel the current callback. """
        pass

    @abstractmethod
    def add_done_callback(self, callback: t.Callable, **options) -> None:
        """ Base function for registering the done callback function. """
        pass
