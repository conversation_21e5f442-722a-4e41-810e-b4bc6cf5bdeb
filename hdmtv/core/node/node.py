#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from ..log import Logger
from abc import abstractmethod
from .client import BaseClient
from .service import BaseService
from .publisher import BasePublisher
from .subscription import BaseSubscription
from .timer import BaseTimer, BaseRate


class BaseNode(object):
    """ Base node defining the operations. """

    def __init__(self, node_name: str, namespace: t.Optional[str] = None, **options) -> None:
        self._node_name = node_name
        self._namespace = namespace
        self._options = options

    @abstractmethod
    def create_publisher(
        self,
        topic: str,
        msg_type: t.Any = None,
        **options
    ) -> BasePublisher:
        """ Abstract function for creating the publisher. """
        pass

    @abstractmethod
    def create_subscription(
        self,
        topic: str,
        callback: t.Callable,
        msg_type: t.Any = None,
        qos_profile: t.Any = 10,
        **options
    ) -> BaseSubscription:
        """ Abstract function for creating the subscription. """
        pass

    @abstractmethod
    def create_timer(self, timer_period_sec: float, callback: t.Callable, **options) -> BaseTimer:
        """ Function for the time creation. """
        pass

    @abstractmethod
    def create_service(
        self,
        srv_name: str,
        callback: t.Callable,
        srv_type: t.Any = None,
        **options
    ) -> BaseService:
        """ Main function for creating the service server. """
        pass

    @abstractmethod
    def create_client(
        self,
        srv_name: str,
        srv_type: t.Any = None,
        **options
    ) -> BaseClient:
        """ Main function for creating the client. """
        pass

    @abstractmethod
    def create_rate(
        self,
        frequency: float,
        **options
    ) -> BaseRate:
        """ Function for the rate creation. """
        pass

    @abstractmethod
    def destroy_publisher(self, publisher: BasePublisher, **options) -> bool:
        """ Function for destroying the given publisher. """
        pass

    @abstractmethod
    def destroy_subscription(self, subscription: BaseSubscription, **options) -> bool:
        """ Function for destroying the given subscription. """
        pass

    @abstractmethod
    def destroy_service(self, service: BaseService, **options) -> bool:
        """ Function for destroying the given service. """
        pass

    @abstractmethod
    def destroy_client(self, client: BaseClient, **options) -> bool:
        """ Function for destroying the given client. """
        pass

    @abstractmethod
    def destroy_timer(self, timer: BaseTimer, **options) -> bool:
        """ Function for destroying the given timer. """
        pass

    @abstractmethod
    def destroy_rate(self, rate: BaseRate, **options) -> bool:
        """ Function for destroying the given rate. """
        pass

    @abstractmethod
    def destroy_node(self,  **options) -> None:
        """ Function for destroying the node itself. """
        pass

    @abstractmethod
    def get_logger(self) -> Logger:
        """ Abstract function for the logger returning. """
        pass