#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod


class BaseTimer(object):
    """ Timer class object. """

    def __init__(self, callback: t.Callable, time_period_ns: float, **options) -> None:
        self._callback = callback
        self._time_period_ns = time_period_ns
        self._options = options

    @abstractmethod
    def destroy(self, **options) -> None:
        """ Function for destroying the timer object. """
        pass


class BaseRate(object):
    """ Rate class object. """

    def __init__(self, timer: BaseTimer, **options) -> None:
        self._timer = timer
        self._options = options

    @abstractmethod
    def sleep(self, **options) -> None:
        """ Block until timer triggers. """
        pass

    @abstractmethod
    def destroy(self, **options) -> None:
        """ Function for destroying the rate object. """
        pass