#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod
from hdmtv.core.config import Config


class _ExecuteFlags(object):
    EMERGENCY: bool = False
    WARNING: bool = False


def reset_execution() -> None:
    """ Reset the emergency. """
    _ExecuteFlags.EMERGENCY = False
    _ExecuteFlags.WARNING = False


def set_emergency() -> None:
    """ Set the emergency stop state. """
    _ExecuteFlags.EMERGENCY = True


def check_emergency() -> bool:
    """ Returns the current emergency state. """
    return _ExecuteFlags.EMERGENCY


class DeviceExecuteError(Exception):
    """ Device execution error. """

    def __init__(self, error_info):
        super().__init__(self)
        self.__error_info = error_info

    def __str__(self):
        return self.__error_info


def executable(func: t.Callable) -> t.Callable:
    """ Wrapper of the executable utility functions. """

    def wrapper(*args, **kwargs) -> t.<PERSON><PERSON>[int, t.Any]:
        """ Inner wrapper of the input function. """
        # if check_emergency():
        #     raise UtilityExecuteError("Emergency stopped.")
        output: t.Any = func(*args, **kwargs)
        if not isinstance(output, tuple):
            raise DeviceExecuteError("Tuple output expected.")
        elif len(output) < 1 or not isinstance(output[0], int):
            raise DeviceExecuteError("Tuple output format invalid.")
        return output

    return wrapper


class Device(object):
    """ Abstract device object. """

    SUCCESS: int = 0
    ERROR: int = -1

    def __init__(self, config: Config) -> None:
        self._mode: str = "execution"
        self._config = config

    @property
    def mode(self) -> str:
        """ Returns the utility's mode. """
        return self._mode

    @property
    @abstractmethod
    def info(self, **options) -> t.Optional[t.Dict[str, t.Any]]:
        """ Returns the utility's information dictionary. """
        pass

    @property
    @abstractmethod
    def is_connected(self) -> bool:
        """ Returns whether the device is connected or not. """
        pass

    @abstractmethod
    def connect(self, **options) -> bool:
        """ Connect to the utility, it returns whether the connection is successful. """
        pass

    @abstractmethod
    def disconnect(self, **options) -> bool:
        """ Disconnect to the utility, it returns whether the connection has been terminated. """
        pass

    @executable
    @abstractmethod
    def initialize(self, **options) -> t.Tuple[int, t.Any]:
        """ Initialize the utility, it returns whether the connection has been terminated. """
        pass

    @executable
    @abstractmethod
    def update_info(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Abstract function for updating the information. """
        pass

    @abstractmethod
    def destroy(self, **options) -> bool:
        """ Destroy to the utility, it releases the resources and returns whether the process has been succeeded. """
        pass

    @abstractmethod
    def suspend(self, **options) -> bool:
        """ Suspend the utility's current activity. """
        pass

    @abstractmethod
    def abort(self, **options) -> bool:
        """ Abort the utility's current activity. """
        pass

    @abstractmethod
    def set_validation_mode(self, **options) -> bool:
        """ Set the validation mode, in which case the utility is applied for validation. """
        self._mode = "validation"
        return True

    @abstractmethod
    def set_execution_mode(self, **options) -> bool:
        """ Set the execution mode, in which case the utility is ready to run. """
        self._mode = "execution"
        return True
