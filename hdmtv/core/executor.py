#!/usr/bin/python
# -*- coding: utf-8 -*-

from abc import abstractmethod
from hdmtv.core.node import BaseNode


class BaseExecutor(object):
    """ Base executor class defined for the node's execution. """

    def __init__(self, **options) -> None:
        self._options = options

    @abstractmethod
    def add_node(self, node: BaseNode, **options) -> bool:
        """ Add a node whose callbacks should be managed by this executor. """
        pass

    @abstractmethod
    def remove_node(self, node: BaseNode, **options) -> None:
        """ Stop managing this node's callbacks. """
        pass

    @abstractmethod
    def spin(self, **options) -> None:
        """ Execute callbacks until shutdown. """
        pass

    @abstractmethod
    def launch(self, **options) -> None:
        """ Launch the current registered nodes. """
        pass

    @abstractmethod
    def shutdown(self, timeout_sec: float = None, **options) -> bool:
        """ Stop executing callbacks and wait for their completion. """
        pass