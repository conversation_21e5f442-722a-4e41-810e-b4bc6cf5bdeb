#!/usr/bin/python
# -*- coding: utf-8 -*-


class Codes(object):
    """ Global codes collection. """

    # General Codes
    SUCCESS: int = 0
    ERROR: int = -1
    LICENSE_EXPIRED: int = 1
    TIMEOUT: int = 2
    DATATYPE_ERROR: int = 3
    SERVER_DISCONNECTED: int = 4
    CLIENT_DISCONNECTED: int = 5
    PUBLISHER_DISCONNECTED: int = 6
    SUBSCRIBER_DISCONNECTED: int = 7
    REQUEST_CANCELLED: int = 8
    REQUEST_SEND_ERROR: int = 9
    REQUEST_DATATYPE_INVALID: int = 11
    RESPONSE_FUNCTION_NOT_FOUND: int = 12
    RESPONSE_PROCESS_ERROR: int = 13
    RESPONSE_TYPE_ERROR: int = 14
