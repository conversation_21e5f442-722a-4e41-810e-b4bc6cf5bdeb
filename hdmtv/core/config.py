#!/usr/bin/python
# -*- coding: utf-8 -*-

import os.path
import typing as t
from loguru import logger
from injector import singleton
from dynaconf import Dynaconf
from marshmallow import EXCLUDE
from dataclasses import is_dataclass
from typing import Optional, Dict, Any, Type
from marshmallow_dataclass import class_schema
from hdmtv.core.types import T
from hdmtv.core.constants import ENV_PREFIX
from hdmtv.core.errors import ConfigValueNotFoundError


@singleton
class Config(object):
    def __init__(self, config_file: t.Optional[str] = None) -> None:
        file_path: str = os.path.dirname(__file__)
        self.app_root: str = os.path.abspath(os.path.join(file_path, ".."))
        self.environment: str = ENV_PREFIX
        config_file: str = config_file or os.path.join(self.app_root, "configs/config.yaml")
        self.settings: Dynaconf = Dynaconf(envvar_prefix=ENV_PREFIX, settings_files=[config_file])

    def get(self) -> Dynaconf:
        return self.settings

    def get_dict(self, prefix: str) -> Optional[Dict[str, Any]]:
        try:
            return self.settings[prefix].to_dict()    # type: ignore
        except KeyError:
            logger.error(f"Config type {prefix} not found in settings")
            return None

    def get_dataclass(self, prefix: str, config_type: Type[T]) -> T:
        if not is_dataclass(config_type):
            raise ValueError(f"{config_type} is not a dataclass.")
        config: Optional[Dict[str, Any]] = self.get_dict(prefix)
        if config is None:
            raise ValueError(f"{prefix} is not found in settings.")
        schema = class_schema(config_type)()
        return schema.load(config, unknown=EXCLUDE)    # type: ignore

    def get_value(self, key: str) -> T:    # type: ignore
        try:
            return self.settings[key]    # type: ignore
        except KeyError:
            raise ConfigValueNotFoundError(f"config key {key} not found in settings")

    def file(self, filename: str) -> str:
        return f"{self.app_root}/{filename}"

