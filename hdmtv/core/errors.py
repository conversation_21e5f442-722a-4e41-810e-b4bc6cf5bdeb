from typing import Optional, Any


class AppError(Exception):
    def __init__(self, message: Optional[str] = None, code: Optional[str] = None, **kwargs: Any) -> None:
        super().__init__(message)
        self.message = message
        self.code = code
        self.kwargs = kwargs

    def __str__(self) -> str:
        if self.code or self.kwargs:
            return "message: {}, code: {}, args: {}".format(self.message, self.code, self.kwargs)
        return super().__str__()


class CodeError(AppError):
    pass


class ConfigValueNotFoundError(AppError):
    pass


class DeviceNotFoundError(AppError):
    pass


class ScenarioNotFoundError(AppError):
    pass


class RpcError(AppError):
    pass
