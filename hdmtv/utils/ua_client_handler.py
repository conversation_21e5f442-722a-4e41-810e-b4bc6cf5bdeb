#!/usr/bin/python
# -*- coding: utf-8 -*-

import copy
import opcua
import typing as t
from loguru import logger
from opcua.common.node import Node
from opcua.ua import VariantType, Variant, DataValue


class UAClientHandler(object):
    """ OPCUA client handler class. """

    def __init__(self, ip_addr: str, port: int, **options) -> None:
        self._ip_addr = ip_addr
        self._port = port
        endpoint: str = "opc.tcp://{}:{}".format(self._ip_addr, self._port)
        self._client: opcua.Client = opcua.Client(endpoint, timeout=60)
        self._root_node: t.Optional[Node] = None
        self._options = options

    def connect(self) -> bool:
        """ Connect to the OPCUA client. """
        try:
            self._client.connect()
            self._root_node = self._client.get_root_node()
        except Exception as expt:
            logger.warning("UAClientHandler: Could not connect to opcua, with exception: {}".format(expt))
            return False
        return True

    def disconnect(self) -> bool:
        """ Disconnect to the OPCUA client. """
        try:
            self._client.disconnect()
        except Exception as expt:
            logger.warning("UAClientHandler: Could not disconnect to opcua, with exception: {}".format(expt))
            return False
        return True

    def get_value(self, identifier: str) -> t.Any:
        """ Function for getting the subscribed data. """
        try:
            node: Node = self._get_node(identifier=identifier)
            node_value = node.get_value()
        except Exception as expt:
            logger.warning(
                "UAClientHandler: Could not get node {} value, the warning message is: {}".format(identifier, expt))
            node_value = None
        return node_value

    def set_value(self, identifier: str, value: t.Any) -> bool:
        """ Function for setting the opcua node value. """
        is_value_set: bool = True
        try:
            node: Node = self._get_node(identifier=identifier)
            value_type: VariantType = node.get_data_type_as_variant_type()
            if isinstance(value, str):
                node_data = DataValue(Variant(UAClientHandler._string_to_byte_list(value), value_type))
            else:
                node_data = DataValue(Variant(value, value_type))
            node.set_value(node_data)
        except Exception as expt:
            logger.warning(
                "UAClientHandler: Could not set node {} value, the warning message is: {}".format(identifier, expt))
            is_value_set = False
        return is_value_set

    def _get_node(self, identifier: str) -> Node:
        """ Get the opcua node given the identifier. """
        new_node_id = f"{identifier}"
        return self._client.get_node(new_node_id)

    @staticmethod
    def _string_to_byte_list(str_data) -> t.List[int]:
        """ Utility function transferring string into byte list. """
        s = copy.deepcopy(str_data)
        if len(s) > 50:
            s = s[:50]
        elif len(s) < 50:
            s += " " * (50 - len(s))
        byte_list = list(s.encode('utf-8'))
        return byte_list


if __name__ == "__main__":
    config: t.Dict[str, t.Any] = {
        "ip_addr": "***********",
        "port": 4840
    }

    client_handler: UAClientHandler = UAClientHandler(**config)
    client_handler.connect()

    index: str = "ns=4;i=6"
    print(client_handler.get_value(identifier=index))
    print(client_handler.set_value(identifier=index, value=22))
    print(client_handler.get_value(identifier=index))

