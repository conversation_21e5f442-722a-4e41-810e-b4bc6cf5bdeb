#!/usr/bin/python
# -*- coding: utf-8 -*-

import json
import typing as t


def load_json(json_file: str) -> t.Dict[str, t.Any]:
    """  Load the json file into python dictionary. """
    with open(json_file.replace("\n", ""), "r", encoding="utf-8") as f:
        dictionary = json.load(f)
        f.close()
    return dictionary


def load_json_utf8(json_file: str) -> t.Dict[str, t.Any]:
    """  Load the json file into python dictionary. """
    with open(json_file.replace("\n", ""), "r", encoding="utf-8") as f:
        dictionary = json.load(f)
        f.close()
    return dictionary


def dump_json(json_file: str, dictionary: t.Dict[str, t.Any]) -> None:
    """ Dump the python dictionary object into json file. """
    with open(json_file.replace("\n", ""), "w", encoding="utf-8") as f:
        json_obj = json.dumps(dictionary, indent=4, ensure_ascii=False)
        f.write(json_obj)
        f.close()
