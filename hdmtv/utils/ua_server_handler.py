#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import json
import opcua
import typing as t
from loguru import logger


class UAServerHandler(object):
    """ Object handling the OPC-UA server. """

    def __init__(
        self,
        ip_addr: t.Optional[str] = None,
        port: t.Optional[int] = None,
        namespace: str = "handler",
        **options
    ) -> None:
        ua_config_file: str = os.path.join(os.path.dirname(__file__), "ua_config.json")
        object_config = UAServerHandler._load_config(ua_config_file)
        self._object_config = object_config
        self._object_matrix: t.Dict[str, t.Any] = dict()
        self._variable_matrix: t.Dict[str, t.Dict[str, t.Any]] = dict()

        self._server: opcua.Server = opcua.Server()
        if ip_addr is not None and port is not None:
            endpoint: str = "opc.tcp://{}:{}".format(ip_addr, port)
            print(endpoint)
            self._server.set_endpoint(endpoint)
        self._namespace = self._server.register_namespace(namespace)
        self._options = options
        self._init_matrices()
        self._server.start()

    @property
    def object_matrix(self) -> t.Dict[str, t.Any]:
        """ Returns the object matrix. """
        return self._object_matrix

    @property
    def variable_matrix(self) -> t.Dict[str, t.Dict[str, t.Any]]:
        """ Returns the variable matrix. """
        return self._variable_matrix

    def get_object(self, object_name: str) -> t.Any:
        """ Get UA object given its name. """
        return self._object_matrix.get(object_name)

    def get_variable(self, object_name: str, variable_name: str) -> t.Any:
        """ Get UA object given the object name and the variable name. """
        obj: t.Any = self._object_matrix.get(object_name, dict())
        return obj.get(variable_name)

    def stop_server(self) -> None:
        """ Stop the current running server. """
        self._server.stop()

    def _init_matrices(self) -> None:
        """ Initialize the node matrix. """
        root_node = self._server.get_objects_node()
        for object_name in self._object_config:
            cur_object = root_node.add_object("ns=2;s=" + object_name, object_name)
            self._object_matrix[object_name] = cur_object
            self._variable_matrix[object_name] = dict()

            for var_name, kwargs in self._object_config[object_name].items():
                if object_name.lower() == "_module":
                    node_name: str = "ns=2;s=" + var_name
                else:
                    node_name: str = "ns=2;s=" + object_name + "." + var_name
                cur_var = cur_object.add_variable(node_name, bname=var_name, **kwargs)
                cur_var.set_writable()
                self._variable_matrix[object_name][var_name] = cur_var

    @staticmethod
    def _load_config(config_file: str) -> t.Dict[str, t.Any]:
        """ Load the configuration file into json dictionary. """
        config_file = config_file.replace("\n", "")
        if not os.path.isfile(config_file) or len(config_file) <= 5 or config_file[-5:] != ".json":
            return dict()
        try:
            with open(config_file, "r") as f:
                config: t.Dict[str, t.Any] = json.load(f)
                f.close()
        except Exception:
            logger.exception('?')
            config: t.Dict[str, t.Any] = dict()
        return config
