#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
SQLite-based data recorder for communication data.

This module provides a comprehensive recording system for HDMTV communication data:
- DataRecorder: Core SQLite-based recording functionality
- Support for various data types with automatic serialization
- Asynchronous recording to avoid blocking main operations
- File size management with automatic recreation
"""

import sqlite3
import threading
import time
import json
import pickle
import base64
import numpy as np
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from collections import defaultdict, deque
from pathlib import Path
from enum import Enum
from concurrent.futures import ThreadPoolExecutor


# =============================================================================
# Data Types and Configuration
# =============================================================================

class DataType(Enum):
    """Supported data types for recording."""
    FLOAT = "float"
    INT = "int"
    STRING = "string"
    BOOL = "bool"
    BYTES = "bytes"
    ARRAY = "array"
    DICT = "dict"
    LIST = "list"
    OBJECT = "object"

# =============================================================================
# Core Data Recorder
# =============================================================================

class DataRecorder:
    """SQLite-based data recorder for topics and messages."""
    
    def __init__(self, 
                 filename: str = "hdmtv_communication.db",
                 buffer_size: int = 1000,
                 auto_commit_interval: float = 5.0,
                 max_file_size_mb: int = 100,
                 topic_blacklist: Optional[List[str]] = None) -> None:
        """Initialize the data recorder with configuration."""
        self.filename = Path(filename)
        
        # Store configuration
        self.buffer_size = buffer_size
        self.auto_commit_interval = auto_commit_interval
        self.max_file_size_mb = max_file_size_mb
        self.topic_blacklist = ["/localhost/slider/slider/heartbeat"]
        
        # State management
        self._is_recording = False
        self._connection: Optional[sqlite3.Connection] = None
        self._lock = threading.RLock()
        
        # Data management
        self._topics: Set[str] = set()
        self._topic_buffers: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=self.buffer_size)
        )
        
        # Background processing
        self._commit_thread: Optional[threading.Thread] = None
        self._stop_commit = threading.Event()
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="recorder")
        self._async_enabled = True
        
        # Statistics
        self._start_time: Optional[float] = None

    def _detect_data_type(self, data: Any) -> str:
        """Automatically detect the data type."""
        type_mapping = {
            bool: "bool", 
            float: "float", 
            int: "int", 
            str: "string",
            bytes: "bytes", 
            list: "list", 
            tuple: "list", 
            dict: "dict",
            np.ndarray: "array"
        }
        return type_mapping.get(type(data), "object")

    def _serialize_data(self, data: Any, data_type: DataType) -> str:
        """Serialize data to string for SQLite storage."""
        if data_type in [DataType.FLOAT, DataType.INT, DataType.BOOL]:
            return str(data)
        elif data_type == DataType.STRING:
            return data
        elif data_type == DataType.BYTES:
            return base64.b64encode(data).decode('utf-8')
        elif data_type == DataType.ARRAY and isinstance(data, np.ndarray):
            return json.dumps({
                'data': data.tolist(),
                'shape': data.shape,
                'dtype': str(data.dtype)
            })
        elif data_type in [DataType.DICT, DataType.LIST, DataType.ARRAY]:
            try:
                return json.dumps(data)
            except (TypeError, ValueError):
                pass
        
        # Fallback to pickle for complex objects
        return base64.b64encode(pickle.dumps(data)).decode('utf-8')

    # -------------------------------------------------------------------------
    # Public Interface
    # -------------------------------------------------------------------------

    def record(self, topic: str, data: Any, timestamp: Optional[str] = None) -> None:
        """Record data to the specified topic."""
        if self._is_recording and self._is_topic_allowed(topic):
            self.record_message_async(topic, data, timestamp)

    def start_recording(self) -> None:
        """Start recording data to SQLite database."""
        with self._lock:
            if self._is_recording:
                raise RuntimeError("Recording is already started")
            
            self._check_and_recreate_file()
            self._initialize_database()
            self._is_recording = True
            self._start_time = time.time()
            self._start_background_tasks()
            
            print(f"Started recording to: {self.filename}")

    def stop_recording(self) -> None:
        """Stop recording and close the database."""
        with self._lock:
            if not self._is_recording:
                return
            
            self._cleanup_recording()
            self._is_recording = False
            print("Recording stopped")

    def flush(self) -> None:
        """Manually flush all buffers."""
        with self._lock:
            if self._is_recording:
                self._flush_all_buffers()

    def __enter__(self):
        """Context manager entry."""
        self.start_recording()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_recording()

    # -------------------------------------------------------------------------
    # Recording Methods
    # -------------------------------------------------------------------------
    
    def record_message(self, topic: str, data: Any, timestamp: Optional[str] = None) -> None:
        """Record a message synchronously."""
        if not self._is_recording:
            raise RuntimeError("Recording not started")
        if not self._is_topic_allowed(topic):
            return
        
        # Auto-detect data type and serialize
        data_type = DataType(self._detect_data_type(data))
        serialized_data = self._serialize_data(data, data_type)
        timestamp = timestamp or str(datetime.now())
        
        with self._lock:
            self._topic_buffers[topic].append((topic, timestamp, data_type, serialized_data))
            
            # Add topic if new
            if topic not in self._topics:
                self._add_topic(topic, data_type)
                self._topics.add(topic)
            
            # Check if buffer needs flushing
            if len(self._topic_buffers[topic]) >= self.buffer_size:
                self._flush_topic_buffer(topic)

    def record_message_async(self, topic: str, data: Any, timestamp: Optional[str] = None) -> None:
        """Record a message asynchronously (non-blocking)."""
        if self._async_enabled and self._is_recording:
            self._executor.submit(self._safe_record, topic, data, timestamp)

    # -------------------------------------------------------------------------
    # Database Management
    # -------------------------------------------------------------------------
    
    def _initialize_database(self) -> None:
        """Initialize SQLite database connection and tables."""
        self._connection = sqlite3.connect(
            self.filename, check_same_thread=False, timeout=30.0
        )
        
        self._create_tables()
        self._add_metadata([
            ('created_at', str(time.time())),
            ('recorder_version', '1.0.0')
        ])
    
    def _create_tables(self) -> None:
        """Create database tables and indexes."""
        cursor = self._connection.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS metadata (
                key TEXT PRIMARY KEY, 
                value TEXT
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS topics (
                topic_name TEXT PRIMARY KEY,
                data_type TEXT,
                message_count INTEGER DEFAULT 0,
                created_at REAL
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                topic TEXT,
                timestamp REAL,
                data_type TEXT,
                data_value TEXT,
                FOREIGN KEY (topic) REFERENCES topics (topic_name)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_topic ON messages (topic)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)")
        
        self._connection.commit()

    def _check_and_recreate_file(self) -> None:
        """Check file size and recreate if necessary."""
        try:
            if self.filename.exists():
                file_size_mb = self.filename.stat().st_size / (1024 * 1024)
                if file_size_mb >= self.max_file_size_mb:
                    # 创建备份文件名 - 直接字符串操作
                    backup_filename = Path(str(self.filename).replace('.db', '_old.db'))
                    
                    # 如果备份文件已存在，先删除
                    if backup_filename.exists():
                        backup_filename.unlink()
                    
                    # 将当前文件重命名为备份文件
                    self.filename.rename(backup_filename)
                    print(f"Starting new recording file: {self.filename}")
        except Exception as e:
            print(f"Error checking file size: {e}")

    # -------------------------------------------------------------------------
    # Data Management
    # -------------------------------------------------------------------------
    
    def _add_metadata(self, metadata: List[tuple]) -> None:
        """Add metadata to database."""
        cursor = self._connection.cursor()
        cursor.executemany("INSERT OR REPLACE INTO metadata (key, value) VALUES (?, ?)", metadata)
        self._connection.commit()
    
    def _add_topic(self, topic: str, data_type: DataType) -> None:
        """Add new topic to database."""
        cursor = self._connection.cursor()
        cursor.execute("""
            INSERT OR IGNORE INTO topics (topic_name, data_type, created_at) 
            VALUES (?, ?, ?)
        """, (topic, data_type.value, time.time()))
        self._connection.commit()
    
    def _flush_topic_buffer(self, topic: str) -> None:
        """Flush buffered messages for a topic to database."""
        buffer = self._topic_buffers[topic]
        if not buffer:
            return
        
        cursor = self._connection.cursor()
        messages = []
        while buffer:
            msg_tuple = buffer.popleft()
            messages.append((msg_tuple[0], msg_tuple[1], msg_tuple[2].value, msg_tuple[3]))
        
        cursor.executemany("""
            INSERT INTO messages (topic, timestamp, data_type, data_value)
            VALUES (?, ?, ?, ?)
        """, messages)
        
        # Simple message count update
        if messages:
            cursor.execute("""
                UPDATE topics SET message_count = message_count + ?
                WHERE topic_name = ?
            """, (len(messages), topic))
        
        self._connection.commit()
    
    def _flush_all_buffers(self) -> None:
        """Flush all topic buffers."""
        for topic in list(self._topic_buffers.keys()):
            self._flush_topic_buffer(topic)

    # -------------------------------------------------------------------------
    # Background Processing
    # -------------------------------------------------------------------------
    
    def _start_background_tasks(self) -> None:
        """Start background auto-commit thread."""
        if self.auto_commit_interval > 0:
            self._stop_commit.clear()
            self._commit_thread = threading.Thread(
                target=self._auto_commit_worker, 
                daemon=True
            )
            self._commit_thread.start()
    
    def _auto_commit_worker(self) -> None:
        """Background worker for automatic buffer flushing."""
        while not self._stop_commit.wait(self.auto_commit_interval):
            with self._lock:
                if self._is_recording:
                    self._flush_all_buffers()

    def _cleanup_recording(self) -> None:
        """Clean up recording resources."""
        self._async_enabled = False
        self._executor.shutdown(wait=True, timeout=5.0)

        if self._commit_thread:
            self._stop_commit.set()
            self._commit_thread.join(timeout=1.0)

        self._flush_all_buffers()
        if self._connection:
            self._connection.commit()
            self._connection.close()
            self._connection = None

    # -------------------------------------------------------------------------
    # Utility Methods
    # -------------------------------------------------------------------------
    
    def _safe_record(self, topic: str, data: Any, timestamp: Optional[str] = None) -> None:
        """Safe wrapper for async recording."""
        try:
            self.record_message(topic, data, timestamp)
        except Exception:
            pass  # Silent error handling
    
    def _is_topic_allowed(self, topic: str) -> bool:
        """Check if topic is not in blacklist."""
        if self.topic_blacklist is None:
            return True
        return topic not in self.topic_blacklist


# =============================================================================
# Module Exports
# =============================================================================

__all__ = [
    'DataRecorder',
    'DataType'
]
