#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import yaml
import typing as t
from injector import singleton
from hdmtv.node import Node
from hdmtv.executor import Executor
from hdmtv.core.log import init_log
from hdmtv.core.config import Config
from hdmtv.recorder import DataRecorder
from hdmtv.core.cdi import binding, get_instance


default_cache_path: str = os.path.join(os.path.expanduser('~'), 'Documents/.hdmtv')
root_path = os.path.dirname(__file__)
if not os.path.exists(default_cache_path):
    os.mkdir(default_cache_path)


def spin(node: Node, executor: Executor = None, **options) -> None:
    """ Execute work and block until the context associated with the executor is shutdown. """
    executor = executor or Executor(**options)
    executor.add_node(node=node)
    executor.launch()
    executor.spin()


def initialize_logger(log_path: t.Optional[str] = None) -> None:
    """ Main function for initializing logger. """
    cache_path: str = log_path or default_cache_path
    log_cache_path: str = os.path.join(cache_path, "log")
    if not os.path.exists(log_cache_path):
        os.mkdir(log_cache_path)

    log_file: str = os.path.join(root_path, "configs/log.yaml").replace("\n", "")
    with open(log_file, 'r', encoding='utf-8') as f:
        result = yaml.load(f.read(), Loader=yaml.FullLoader)

    is_logger_path: bool = False
    for i, handler_config in enumerate(result["handlers"]):
        if handler_config["sink"] != "ext://sys.stdout":
            log_path, _ = os.path.split(handler_config["sink"])
            if not os.path.exists(log_path):
                try:
                    os.mkdir(log_path)
                except Exception as expt:
                    print(expt)
                    is_logger_path = False
            else:
                is_logger_path = True
            break

    if is_logger_path:
        init_log(config_path=os.path.join(root_path, "configs").replace("\n", ""))
    else:
        raise AssertionError("无法获取LOG文件夹地址")


def initialize_config(config_file: t.Optional[str] = None) -> None:
    """ Function for binding all the dependencies. """
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)


def initialize_recorder() -> None:
    """ Function for initializing data recorder. """
    # 获取配置
    config: Config = get_instance(clazz=Config)
    recorder_config: t.Optional[t.Dict[str, t.Any]] = config.get_dict(prefix="RECORDER")
    if recorder_config is None or not recorder_config.get("enable", False):
        return

    # 直接创建记录器实例
    data_recorder = DataRecorder(**recorder_config)
    
    # 启动记录
    data_recorder.start_recording()
    
    # 通过依赖注入系统注册
    binding(interface=DataRecorder, to=data_recorder, scope=singleton)


def init(**options) -> None:
    """ Initialize hdmtv communications for a given context. """
    initialize_logger(log_path=options.get("log_path"))
    initialize_config(config_file=options.get("config_file"))
    initialize_recorder()


def launch(**options) -> None:
    """ Function for launching all the nodes that has been instantiated. """
    launcher: Executor = Executor(**options)
    launcher.launch()

