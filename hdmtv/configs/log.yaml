handlers:
  - sink: ext://sys.stdout
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS Z} | {level: <8} | p-{process: <5} | t-{thread: <10} | {name}:{function}:{line} - {message}'
    level: INFO
  - sink: C:/Users/<USER>/Documents/.hdmtv/log-{time:YYYY-MM-DD}.log
    format: '{time:YYYY-MM-DD HH:mm:ss.SSS Z} | {level: <8} | p-{process: <5} | t-{thread: <10} | {name}:{function}:{line} - {message}'
    enqueue: true
    serialize: false
    level: INFO
    rotation: '1 day'
    retention: '100 days'

extra:
  task-id: '-'
