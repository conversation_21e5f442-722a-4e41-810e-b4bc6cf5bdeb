#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
from pathlib import Path
from argparse import ArgumentParser

def _app_root() -> str:
    return str(Path(__file__).parent)


if __name__ == '__main__':
    parser: ArgumentParser = ArgumentParser(description="Automation args")
    parser.add_argument("-c", "--config", type=str, default=None, required=False, help="Config file")
    parser.add_argument("-e", "--environment", type=str, required=False, help="Config file",
                        default=os.environ.get("ENVIRONMENT"))
    args = parser.parse_args()

