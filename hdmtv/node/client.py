#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import socket
import msgpack
import typing as t
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import Future as ThreadFuture
from hdmtv.core.flags import Codes
from hdmtv.node.task import Future
from hdmtv.core.config import Config
from hdmtv.core.cdi import get_instance
from hdmtv.node.utils import ServiceParser
from hdmtv.core.node import BaseClient, Request, Response
# from hdmtv.recorder import recorder_manager


class _RPCClient(object):
    """ RPC client definition class. """

    def __init__(self, host: str = 'localhost', port: int = 9999) -> None:
        self._host = host
        self._port = port
        config: Config = get_instance(clazz=Config)
        max_workers: int = config.get_dict(prefix="TIMER").get("max_workers", 8)
        self._executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=max(1, max_workers))

    def call(self, name: str, *args, **kwargs) -> Response:
        """ Function for processing the synchronous calling function. """
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            try:
                sock.connect((self._host, self._port))
            except Exception as expt:
                return Response(code=Codes.SERVER_DISCONNECTED, message="无法连接到服务端: {}".format(str(expt)))

            # 1. 准备请求数据
            request = {
                'func': name,
                'args': args,
                'kwargs': kwargs
            }
            request_data = msgpack.packb(request, use_bin_type=True)

            # # 记录发送给服务端的数据
            # print(999, request)
            # recorder_manager.record(f"{name}/request", "client->server")

            # 2. 发送请求（带4字节长度头）
            header = len(request_data).to_bytes(4, 'big')
            sock.sendall(header + request_data)

            # 3. 接收响应头
            header = sock.recv(4)
            if not header:
                return Response(code=Codes.RESPONSE_TYPE_ERROR, message="未收到响应头")

            data_len = int.from_bytes(header, 'big')

            # 4. 接收完整响应数据
            data = bytearray()
            while len(data) < data_len:
                remaining = data_len - len(data)
                chunk = sock.recv(min(4096, remaining))
                if not chunk:
                    return Response(code=Codes.RESPONSE_TYPE_ERROR, message="响应数据不完整")
                data.extend(chunk)

            # 5. 处理响应
            try:
                response: Response = Response(**msgpack.unpackb(data, raw=False))
            except Exception as expt:
                response: Response = Response(code=Codes.RESPONSE_TYPE_ERROR, message=str(expt))
        return response

    def call_async(self, name: str, *args, **kwargs) -> Future:
        """ Function for processing the asynchronous calling through multi-threading. """
        future: ThreadFuture = self._executor.submit(self.call, name, *args, **kwargs)
        return Future(future=future)

    def shutdown(self, wait: bool = False) -> None:
        """ Shutdown the current client. """
        self._executor.shutdown(wait=wait)


class Client(BaseClient):
    """ Base client class. """

    _client_matrix: t.Dict[str, t.Optional[_RPCClient]] = dict()
    _endpoint_ref_count: t.Dict[str, int] = dict()  # 新增：跟踪每个endpoint的引用计数
    _is_start: bool = False
    _is_destroyed: bool = False

    def __init__(self, srv_name: str, **options) -> None:
        super().__init__(srv_name=srv_name, **options)
        self._parser: ServiceParser = ServiceParser(url=srv_name)
        self._endpoint: str = self._parser.endpoint
        self._is_instance_destroyed: bool = False  # 实例级别的销毁标志

        # 初始化endpoint相关的数据结构
        if self._endpoint not in self._client_matrix:
            self._client_matrix[self._endpoint] = None
            self._endpoint_ref_count[self._endpoint] = 0

        # 增加引用计数
        self._endpoint_ref_count[self._endpoint] += 1

        # 检查并创建_RPCClient
        self._ensure_rpc_client()

        # 获取客户端实例
        self._client: _RPCClient = self._client_matrix[self._endpoint]

    def _ensure_rpc_client(self) -> None:
        """ 确保当前endpoint的_RPCClient已创建 """
        # 检查是否需要创建_RPCClient
        if self._client_matrix.get(self._endpoint) is None:
            # 需要创建_RPCClient
            self._init_single_rpc_client(self._endpoint)

    def _init_single_rpc_client(self, endpoint: str) -> None:
        """ 初始化单个endpoint的RPC客户端 """
        if self._client_matrix.get(endpoint) is None:
            # 创建_RPCClient
            client = _RPCClient(host=self._parser.host, port=self._parser.port)
            self._client_matrix[endpoint] = client

    def call(self, request: Request, **options) -> Response:
        """ Function for performing the RPC procedure. """
        return self._client.call(self._parser.func_string, request)

    def call_async(self, request: Request, **options) -> Future:
        """ Function for performing the asynchronous call procedure. """
        return self._client.call_async(self._parser.func_string, request)

    def destroy(self, **options) -> None:
        """ Destroy the current client. """
        if not self._is_instance_destroyed:
            # 减少当前endpoint的引用计数
            if self._endpoint in self._endpoint_ref_count:
                self._endpoint_ref_count[self._endpoint] -= 1

                # 如果引用计数为0，释放_RPCClient资源
                if self._endpoint_ref_count[self._endpoint] <= 0:
                    if self._endpoint in self._client_matrix and self._client_matrix[self._endpoint] is not None:
                        # 关闭线程池
                        client = self._client_matrix[self._endpoint]
                        client.shutdown(wait=False)
                        self._client_matrix[self._endpoint] = None

                    # 从引用计数中移除
                    if self._endpoint in self._endpoint_ref_count:
                        del self._endpoint_ref_count[self._endpoint]

            self._is_instance_destroyed = True


if __name__ == "__main__":
    from injector import singleton
    from hdmtv.core.cdi import binding

    config_file: str = r"D:\Gitlab\hdmtv\hdmtv\configs\config.yaml"
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)

    def callback_func(response: Response):
        print(type(response))
        print(response)

    client: Client = Client(srv_name="/localhost/say_hello", callback=callback_func)

    while True:
        input_info = input(">>")
        if input_info.lower() == "e":
            break
        elif input_info.lower() == "a":
            f: Future = client.call_async(request=Request(data={"header": "head", "data": {"a": 1}}))
            f.add_done_callback(callback=callback_func)
            while not f.done:
                time.sleep(0.01)
            print("The current result type is:", type(f.result))
            print(f.result())
        else:
            start = time.time()
            result = client.call(request=Request(data={"header": "head", "data": {"a": 1}}))
            print("The running time is {} seconds.".format(time.time() - start))
            print(result)
    client.destroy()
