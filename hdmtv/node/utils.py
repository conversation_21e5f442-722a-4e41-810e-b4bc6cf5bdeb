#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from functools import lru_cache
from abc import abstractmethod
from hdmtv.core.config import Config
from hdmtv.core.cdi import get_instance


class UrlParser(object):
    """ Base URL parser class. """

    def __init__(self, url: str, **options) -> None:
        self._url = url
        self._segments: t.List[str] = self._segment_url(url=url)
        self._is_valid: bool = self._check_validation()
        self._options = options

    @property
    @lru_cache
    def is_valid(self) -> bool:
        """ Returns whether the input url is valid. """
        return self._is_valid

    @staticmethod
    def _segment_url(url: str) -> t.List[str]:
        """ Segment the url. """
        raw_segments: t.List[str] = url.split("/")
        segments: t.List[str] = list()
        for part in raw_segments:
            if len(part) > 0:
                segments.append(part)
        return segments

    @abstractmethod
    def _check_validation(self) -> bool:
        """ Function for checking the url's validation. """
        pass
    

class TopicUrlParser(UrlParser):
    """ URL parser class for topic. """

    def __init__(self, url: str, val: t.Any = None, **options) -> None:
        self._val = val
        self._config: Config = get_instance(clazz=Config)
        self._endpoint_matrix: t.Dict[str, str] = self._config.get_dict(prefix="TOPIC_ENDPOINTS") or dict()
        super().__init__(url=url, **options)

    def _check_validation(self) -> bool:
        """ Function for checking the url's validation. """
        if len(self._segments) < 4:
            return False
        elif self._segments[0] not in self._endpoint_matrix:
            self._endpoint_matrix[self._segments[0]] = self._segments[0]

        endpoint: str = self._endpoint_matrix[self._segments[0]]
        if ":" not in endpoint:
            return False
        elif not endpoint.split(":")[-1].isdigit():
            return False
        return True

    @property
    def val(self) -> t.Any:
        """ Get the initialized value. """
        return self._val

    @property
    @lru_cache
    def endpoint(self) -> t.Optional[str]:
        """ Get the opcua endpoint. """
        if self.is_valid:
            return self._endpoint_matrix[self._segments[0]]

    @property
    @lru_cache
    def namespace(self) -> t.Optional[str]:
        """ Get the opcua namespace. """
        if self.is_valid:
            return self._segments[1]

    @property
    @lru_cache
    def browse_name(self) -> t.Optional[str]:
        """ Get the opcua object's browse name. """
        browse_name: str = ""
        if self.is_valid:
            for i in range(2, len(self._segments)):
                browse_name += self._segments[i] + "_"
            return browse_name[:-1]

    @property
    @lru_cache
    def object_levels(self) -> t.Optional[t.List[str]]:
        """ Get the opcua object levels. """
        if self.is_valid:
            return self._segments[2:]

    def get_node_id(self, ns_idx: int) -> t.Optional[str]:
        """ Get the opcua node id. """
        node_id: str = ""
        if self.is_valid:
            for i in range(2, len(self._segments)):
                node_id += self._segments[i] + "."
            return "ns={};s=".format(ns_idx) + node_id[:-1]


class ServiceParser(UrlParser):
    """ URL parser class for service. """

    def __init__(self, url: str, **options) -> None:
        self._config: Config = get_instance(clazz=Config)
        self._endpoint_matrix: t.Dict[str, str] = self._config.get_dict(prefix="SERVICE_ENDPOINTS") or dict()
        super().__init__(url=url, **options)

    def _check_validation(self) -> bool:
        """ Function for checking the url's validation. """
        if len(self._segments) < 2:
            return False
        elif self._segments[0] not in self._endpoint_matrix:
            self._endpoint_matrix[self._segments[0]] = self._segments[0]

        endpoint: str = self._endpoint_matrix[self._segments[0]]
        if ":" not in endpoint:
            return False
        elif not endpoint.split(":")[-1].isdigit():
            return False
        return True

    @property
    @lru_cache
    def endpoint(self) -> t.Optional[str]:
        """ Get the service's endpoint. """
        if self.is_valid:
            return self._endpoint_matrix[self._segments[0]]

    @property
    @lru_cache
    def host(self) -> t.Optional[str]:
        """ Get the service's host. """
        if self.is_valid:
            return self.endpoint.split(":")[0]

    @property
    @lru_cache
    def port(self) -> t.Optional[int]:
        """ Get the service's port. """
        if self.is_valid:
            return int(self.endpoint.split(":")[-1])

    @property
    @lru_cache
    def namespace(self) -> t.Optional[str]:
        """ Get the service's namespace. """
        if self.is_valid:
            return self._segments[1]

    @property
    @lru_cache
    def func_string(self) -> t.Optional[str]:
        """ Get the service's namespace. """
        func_string: str = ""
        if self.is_valid:
            for i in range(1, len(self._segments)):
                func_string += self._segments[i] + "_"
            return func_string[:-1]


if __name__ == "__main__":
    from injector import singleton
    from hdmtv.core.cdi import binding
    config_file: str = r"D:\Gitlab\hdmtv\hdmtv\configs\config.yaml"
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)

    topic_url: str = "/localhost/ns/robot/j1"
    service_url: str = "/localhost/my_class/my_func"

    topic_parser: TopicUrlParser = TopicUrlParser(url=topic_url)
    service_parser: ServiceParser = ServiceParser(url=service_url)

    print(topic_parser.is_valid)
    print(topic_parser.endpoint)
    print(topic_parser.namespace)
    print(topic_parser.browse_name)
    print(topic_parser.object_levels)
    print(topic_parser.get_node_id(ns_idx=2))

    # print(service_parser.is_valid)
    # print(service_parser.endpoint)
    # print(service_parser.host)
    # print(service_parser.port)
    # print(service_parser.namespace)
    # print(service_parser.func_string)