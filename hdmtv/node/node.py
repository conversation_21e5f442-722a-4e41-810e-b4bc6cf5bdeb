#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from loguru import logger
from hdmtv.core.node import BaseNode
from hdmtv.node.timer import Timer, Rate
from hdmtv.node.client import Client
from hdmtv.node.service import Service
from hdmtv.node.publisher import Publisher
from hdmtv.node.subscription import Subscription


class Node(BaseNode):
    """ Customized node defining the operations. """

    def __init__(self, node_name: str, namespace: t.Optional[str] = None, **options) -> None:
        super().__init__(node_name=node_name, namespace=namespace, **options)
        self._publisher_matrix: t.Dict[str, Publisher] = dict()
        self._subscription_matrix: t.Dict[str, Subscription] = dict()
        self._service_matrix: t.Dict[str, Service] = dict()
        self._client_matrix: t.Dict[str, Client] = dict()
        self._timers: t.List[Timer] = list()
        self._rates: t.List[Rate] = list()

    def create_publisher(
        self,
        topic: str,
        val: t.Any = None,
        msg_type: t.Any = None,
        **options
    ) -> Publisher:
        """ Main function for creating the publisher. """
        if val is None:
            raise AssertionError("Please give an initial value of the publisher.")
        publisher: Publisher = Publisher(topic=topic, val=val, sg_type=msg_type, **options)
        self._publisher_matrix[topic] = publisher
        return publisher

    def create_subscription(
        self,
        topic: str,
        callback: t.Callable,
        msg_type: t.Any = None,
        qos_profile: t.Any = 10,
        **options
    ) -> Subscription:
        """ Main function for creating the subscription. """
        subscription: Subscription = Subscription(
            topic=topic, callback=callback, msg_type=msg_type, qos_profile=qos_profile, **options)
        self._subscription_matrix[topic] = subscription
        return subscription

    def create_service(
        self,
        srv_name: str,
        callback: t.Callable,
        srv_type: t.Any = None,
        **options
    ) -> Service:
        """ Main function for creating the service server. """
        service: Service = Service(srv_name=srv_name, callback=callback, srv_type=srv_type, **options)
        self._service_matrix[srv_name] = service
        return service

    def create_client(
        self,
        srv_name: str,
        srv_type: t.Any = None,
        **options
    ) -> Client:
        """ Main function for creating the client. """
        client: Client = Client(srv_name=srv_name, srv_type=srv_type, **options)
        self._client_matrix[srv_name] = client
        return client

    def create_rate(
        self,
        frequency: float,
        **options
    ) -> Rate:
        """ Function for the rate creation. """
        if frequency <= 0:
            raise ValueError('frequency must be > 0')
        period: float = 1.0 / frequency
        timer = self.create_timer(period, **options)
        rate: Rate = Rate(timer=timer, **options)
        self._rates.append(rate)
        return rate

    def create_timer(self, timer_period_sec: float, callback: t.Callable, **options) -> Timer:
        """ Function for the time creation. """
        timer_period_ns: float = timer_period_sec * 1000000.0
        timer: Timer = Timer(callback=callback, time_period_ns=timer_period_ns, **options)
        self._timers.append(timer)
        return timer

    def destroy_publisher(self, publisher: Publisher, **options) -> bool:
        """ Function for destroying the given publisher. """
        try:
            publisher.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_subscription(self, subscription: Subscription, **options) -> bool:
        """ Function for destroying the given subscription. """
        try:
            subscription.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_service(self, service: Service, **options) -> bool:
        """ Function for destroying the given service. """
        try:
            service.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_client(self, client: Client, **options) -> bool:
        """ Function for destroying the given client. """
        try:
            client.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_timer(self, timer: Timer, **options) -> bool:
        """ Function for destroying the given timer. """
        try:
            timer.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_rate(self, rate: Rate, **options) -> bool:
        """ Function for destroying the given rate. """
        try:
            rate.destroy()
        except Exception as expt:
            logger.error(str(expt))
            return False
        return True

    def destroy_node(self,  **options) -> None:
        """ Function for destroying the node itself. """
        for topic in self._publisher_matrix:
            self._publisher_matrix[topic].destroy()
        for topic in self._subscription_matrix:
            self._subscription_matrix[topic].destroy()
        for srv_name in self._service_matrix:
            self._service_matrix[srv_name].destroy()
        for srv_name in self._client_matrix:
            self._client_matrix[srv_name].destroy()
        for timer in self._timers:
            timer.destroy()
        for rate in self._rates:
            rate.destroy()

    def get_logger(self) -> logger:
        """ Function for the logger returning. """
        return logger
