#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from concurrent.futures import Future as ThreadFuture
from hdmtv.core.node import BaseFuture, Response


class _CallbackHandler(object):
    """ Callback handler class, which wraps the callback function. """

    def __init__(self, callback: t.Callable) -> None:
        self._callback = callback

    def callback_wrapper(self, future: ThreadFuture) -> None:
        """ Callback wrapper function. """
        return self._callback(future.result())


class Future(BaseFuture):
    """ Future class for the data exchange. """

    def __init__(self, **options) -> None:
        super().__init__(**options)
        self._future: t.Optional[ThreadFuture] = options.get("future")
        if self._future is None or not isinstance(self._future, ThreadFuture):
            raise AssertionError("Invalid future input.")

    def result(self, **options) -> t.Optional[Response]:
        """ Returns the callback result. """
        return self._future.result()

    def done(self, **options) -> bool:
        """ Returns whether the task has been done. """
        return self._future.done()

    def cancelled(self, **options) -> bool:
        """ Returns whether the task has been cancelled. """
        return self._future.cancelled()

    def cancel(self, **options) -> bool:
        """ Cancel the current callback. """
        return self._future.cancel()

    def add_done_callback(self, callback: t.Callable, **options) -> None:
        """ Base function for registering the done callback function. """
        return self._future.add_done_callback(fn=_CallbackHandler(callback=callback).callback_wrapper)
