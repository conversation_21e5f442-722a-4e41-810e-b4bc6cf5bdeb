#!/usr/bin/python
# -*- coding: utf-8 -*-

import asyncio
import typing as t
from asyncua import Server
from typing import TypeVar
from threading import Thread
from hdmtv.core.node import BasePublisher
from hdmtv.node.utils import TopicUrlParser

MsgType = TypeVar('MsgType')


class _ServerRunner(Thread):
    """ OPCUA server runner class. """

    def __init__(self, server: Server, **options) -> None:
        super().__init__(**options)
        self._is_exit: bool = False
        self._server = server
        self._options = options

    async def start_server(self) -> None:
        """ Main function for staring the server. """
        async with self._server:
            while not self._is_exit:
                await asyncio.sleep(1.0)

    def exit(self) -> None:
        """ Function for exiting the running loop. """
        self._is_exit = True

    def run(self) -> None:
        """ Main running loop. """
        asyncio.run(self.start_server())


class Publisher(BasePublisher):
    """ Base publisher defining the publishing used by node. """

    _server_matrix: t.Dict[str, t.Optional[Server]] = dict()
    _runner_matrix: t.Dict[str, t.Optional[_ServerRunner]] = dict()
    _server_namespace_matrix: t.Dict[str, t.Dict[str, t.Optional[int]]] = dict()
    _topic_parser_list: t.List[TopicUrlParser] = list()
    _variable_matrix: t.Dict[str, t.Any] = dict()
    _endpoint_ref_count: t.Dict[str, int] = dict()  # 新增：跟踪每个endpoint的引用计数

    def __init__(self, topic: str, val: t.Any, msg_type: t.Optional[MsgType] = None, **options) -> None:
        super().__init__(topic=topic, msg_type=msg_type, **options)
        self._parser: TopicUrlParser = TopicUrlParser(url=topic, val=val)
        self._namespace: str = self._parser.namespace
        self._endpoint: str = self._parser.endpoint
        self._topic_parser_list.append(self._parser)
        self._is_instance_destroyed: bool = False  # 实例级别的销毁标志

        # 检查并创建_ServerRunner，同时会处理引用计数
        self._ensure_server_runner()

    def _ensure_server_runner(self) -> None:
        """ 确保当前endpoint的_ServerRunner已创建并启动 """
        try:
            event_loop = asyncio.get_event_loop()
        except RuntimeError:
            event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(event_loop)

        # 初始化endpoint相关的数据结构
        if self._endpoint not in self._server_matrix:
            self._server_matrix[self._endpoint] = None
            self._server_namespace_matrix[self._endpoint] = dict()
            self._endpoint_ref_count[self._endpoint] = 0

        # 增加引用计数
        self._endpoint_ref_count[self._endpoint] += 1

        # 检查是否需要创建_ServerRunner
        if self._runner_matrix.get(self._endpoint) is None:
            # 需要创建并启动_ServerRunner
            event_loop.run_until_complete(self._init_single_server(self._endpoint, self._namespace))
        else:
            # _ServerRunner已存在，只需要确保namespace和变量已创建
            if self._server_namespace_matrix[self._endpoint].get(self._namespace) is None:
                event_loop.run_until_complete(self._init_single_namespace(self._endpoint, self._namespace))
            event_loop.run_until_complete(self._create_variables_for_endpoint(self._endpoint))

    async def _init_single_server(self, endpoint: str, namespace: str) -> None:
        """ 初始化单个endpoint的服务器和运行器 """
        if self._server_matrix.get(endpoint) is None:
            # 创建并初始化服务器
            server = Server()
            server.iserver.history_manager.enable = False
            await server.init()

            # 标准化endpoint格式
            server.set_endpoint(self._parser.endpoint)
            self._server_matrix[endpoint] = server

            # 注册命名空间
            await self._init_single_namespace(endpoint=endpoint, namespace=namespace)
            # 创建变量节点
            await self._create_variables_for_endpoint(endpoint)

        # 创建并启动_ServerRunner
        if self._runner_matrix.get(endpoint) is None:
            server: Server = self._server_matrix[endpoint]
            server_runner: _ServerRunner = _ServerRunner(server=server)
            self._runner_matrix[endpoint] = server_runner
            server_runner.start()

    async def _init_single_namespace(self, endpoint: str, namespace: str) -> None:
        """ 初始化单个namespace的服务器和运行器 """
        server: Server = self._server_matrix[endpoint]
        if self._server_namespace_matrix[endpoint].get(namespace) is None:
            idx: int = await server.register_namespace(namespace)
            self._server_namespace_matrix[endpoint][namespace] = idx

    async def _create_variables_for_endpoint(self, endpoint: str) -> None:
        """ 为指定endpoint创建变量节点 """
        server: Server = self._server_matrix[endpoint]

        # 使用类级别的节点缓存，避免重复创建
        if not hasattr(self.__class__, '_node_cache'):
            self.__class__._node_cache = {}

        node_cache = self.__class__._node_cache

        # 只处理当前实例的parser
        parser = self._parser
        if parser.endpoint != endpoint:
            return

        namespace: str = parser.namespace
        browse_name: str = parser.browse_name
        object_levels: t.List[str] = parser.object_levels

        # 检查object_levels是否有效
        if object_levels is None or len(object_levels) == 0:
            return

        ns_index: int = self._server_namespace_matrix[endpoint][namespace]

        # 构建完整的对象路径作为缓存键
        obj_name: str = object_levels[0]
        cache_key = f"{endpoint}:{ns_index}:{obj_name}"
        obj_node: t.Any = node_cache.get(cache_key)

        if obj_node is None:
            try:
                obj_node = await server.nodes.objects.add_object(
                    "ns={};s={}".format(ns_index, obj_name), bname=obj_name)
                node_cache[cache_key] = obj_node
            except Exception:
                # 如果对象已存在，尝试获取现有对象
                try:
                    obj_node = server.get_node("ns={};s={}".format(ns_index, obj_name))
                    node_cache[cache_key] = obj_node
                except Exception:
                    return

        for i in range(1, len(object_levels), 1):
            obj_name += "." + object_levels[i]
            cache_key = f"{endpoint}:{ns_index}:{obj_name}"

            if i == len(object_levels) - 1:
                # 创建变量节点
                node_id: str = parser.get_node_id(ns_idx=ns_index)
                if node_id not in self._variable_matrix:  # 避免重复创建
                    try:
                        var = await obj_node.add_variable(node_id, bname=browse_name, val=parser.val)
                        await var.set_writable(writable=False)
                        self._variable_matrix[node_id] = var
                    except Exception:
                        # 如果变量已存在，尝试获取现有变量
                        try:
                            var = server.get_node(node_id)
                            self._variable_matrix[node_id] = var
                        except Exception:
                            pass
            else:
                # 创建或获取中间对象节点
                if cache_key in node_cache:
                    obj_node = node_cache[cache_key]
                else:
                    try:
                        obj_node = await obj_node.add_object(
                            "ns={};s={}".format(ns_index, obj_name), bname=obj_name)
                        node_cache[cache_key] = obj_node
                    except Exception:
                        # 如果对象已存在，尝试获取现有对象
                        try:
                            obj_node = server.get_node("ns={};s={}".format(ns_index, obj_name))
                            node_cache[cache_key] = obj_node
                        except Exception:
                            return

    def publish(self, msg: t.Any, **options) -> None:
        """ Send a message to the topic for the publisher. """
        ns_idx: int = self._server_namespace_matrix[self._endpoint][self._namespace]
        node_id: str = self._parser.get_node_id(ns_idx=ns_idx)
        var = self._variable_matrix[node_id]

        try:
            event_loop = asyncio.get_event_loop()  # 主线程调用
        except Exception as expt:
            event_loop = asyncio.new_event_loop()  # 子线程调用
            asyncio.set_event_loop(event_loop)
        event_loop.run_until_complete(var.write_value(msg))

    def destroy(self, **options) -> None:
        """ Function for destroying the publisher. """
        if not self._is_instance_destroyed:
            # 减少当前endpoint的引用计数
            if self._endpoint in self._endpoint_ref_count:
                self._endpoint_ref_count[self._endpoint] -= 1

                # 如果引用计数为0，释放_ServerRunner资源
                if self._endpoint_ref_count[self._endpoint] <= 0:
                    if self._endpoint in self._runner_matrix and self._runner_matrix[self._endpoint] is not None:
                        self._runner_matrix[self._endpoint].exit()
                        self._runner_matrix[self._endpoint] = None

                    # 清理相关资源
                    if self._endpoint in self._server_matrix:
                        self._server_matrix[self._endpoint] = None
                        self._server_namespace_matrix.pop(self._endpoint)

                    # 清理变量矩阵中属于该endpoint的变量
                    self._cleanup_variables_for_endpoint(self._endpoint)

                    # 从引用计数中移除
                    if self._endpoint in self._endpoint_ref_count:
                        del self._endpoint_ref_count[self._endpoint]

            # 从topic_parser_list中移除当前实例的parser
            if self._parser in self._topic_parser_list:
                self._topic_parser_list.remove(self._parser)

            self._is_instance_destroyed = True

    def _cleanup_variables_for_endpoint(self, endpoint: str) -> None:
        """ 清理指定endpoint的变量 """
        # 找到属于该endpoint的所有变量并移除
        keys_to_remove = list()
        for node_id in self._variable_matrix:
            # 通过解析node_id来判断是否属于该endpoint
            # 这里需要根据实际的node_id格式来判断
            for parser in self._topic_parser_list:
                if parser.endpoint == endpoint:
                    # 获取该parser对应的node_id
                    if endpoint in self._server_namespace_matrix:
                        for namespace in self._server_namespace_matrix[endpoint]:
                            ns_idx = self._server_namespace_matrix[endpoint][namespace]
                            if ns_idx is not None:
                                expected_node_id = parser.get_node_id(ns_idx=ns_idx)
                                if node_id == expected_node_id:
                                    keys_to_remove.append(node_id)

        for key in keys_to_remove:
            if key in self._variable_matrix:
                del self._variable_matrix[key]


if __name__ == "__main__":
    from hdmtv.core.config import Config
    from injector import singleton
    from hdmtv.core.cdi import binding, get_instance
    config_file: str = r"D:\Gitlab\hdmtv\hdmtv\configs\config.yaml"
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)

    publisher_1: Publisher = Publisher(topic="/localhost/ns1/robot/j1", val=25.0)
    publisher_2: Publisher = Publisher(topic="/localhost/ns1/robot/axis_name", val="axis")

    while True:
        value = input("\n>>")
        if value.lower() == "e":
            break
        elif value == "1":
            publisher_1.destroy()
        else:
            try:
                float_value = float(value)
                publisher_1.publish(msg=float_value)
                publisher_2.publish(msg=value)
            except Exception as expt:
                publisher_1.destroy()
                publisher_2.destroy()
    publisher_1.destroy()
    publisher_2.destroy()