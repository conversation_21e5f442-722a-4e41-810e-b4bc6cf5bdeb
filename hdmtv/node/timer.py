#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import queue
import threading
import typing as t
from loguru import logger
from hdmtv.core.config import Config
from hdmtv.core.cdi import get_instance
from hdmtv.core.node import BaseTimer, BaseRate


class EfficientTimerManager:
    """Efficient timer manager using a single scheduler thread and callback queue."""

    _instance: t.Optional['EfficientTimerManager'] = None
    _lock = threading.Lock()

    def __init__(self):
        self._active_timers: t.Dict[str, 'EfficientTimer'] = {}
        self._scheduler_thread: t.Optional[threading.Thread] = None
        self._callback_queue: queue.Queue = queue.Queue()
        self._worker_threads: t.List[threading.Thread] = []
        self._shutdown_event = threading.Event()

    @classmethod
    def get_instance(cls) -> 'EfficientTimerManager':
        """Get singleton instance of EfficientTimerManager."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    cls._instance._initialize()
        return cls._instance

    def _initialize(self) -> None:
        """Initialize the timer manager."""
        config: Config = get_instance(clazz=Config)
        timer_config = config.get_dict(prefix="TIMER")
        num_workers = timer_config.get("max_workers", 4)  # Small number of worker threads

        # Start worker threads for callback execution
        for i in range(num_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f'hdmtv_timer_worker_{i}',
                daemon=True
            )
            worker.start()
            self._worker_threads.append(worker)

        # Start scheduler thread
        self._scheduler_thread = threading.Thread(
            target=self._scheduler_loop,
            name='hdmtv_timer_scheduler',
            daemon=True
        )
        self._scheduler_thread.start()

    def _worker_loop(self) -> None:
        """Worker thread loop for executing callbacks."""
        while not self._shutdown_event.is_set():
            try:
                # Get callback from queue with timeout
                callback, timer_id = self._callback_queue.get(timeout=1.0)
                if callback is None:  # Shutdown signal
                    break

                # Execute callback safely
                try:
                    callback()
                except Exception as e:
                    logger.error(f"Timer {timer_id} callback error: {e}", exc_info=True)

                self._callback_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Worker thread error: {e}")

    def _scheduler_loop(self) -> None:
        """Main scheduler loop that manages all timers."""
        while not self._shutdown_event.is_set():
            current_time = time.time()

            # Check all active timers
            for timer in list(self._active_timers.values()):
                if timer.should_execute(current_time):
                    # Queue callback for execution
                    try:
                        self._callback_queue.put_nowait((timer.get_callback(), timer.get_id()))
                        timer.update_next_run(current_time)
                    except queue.Full:
                        logger.warning(f"Callback queue full, skipping timer {timer.get_id()}")

            # Sleep for a short time (10ms resolution)
            time.sleep(0.01)

    def schedule_timer(self, timer_id: str, callback: t.Callable, interval: float) -> None:
        """Schedule a new timer task."""
        if timer_id in self._active_timers:
            self.cancel_timer(timer_id)

        timer = EfficientTimer(timer_id, callback, interval)
        self._active_timers[timer_id] = timer

    def cancel_timer(self, timer_id: str) -> None:
        """Cancel a specific timer."""
        self._active_timers.pop(timer_id, None)

    def get_active_timer_count(self) -> int:
        """Get the number of active timers."""
        return len(self._active_timers)

    def shutdown(self) -> None:
        """Shutdown the timer manager."""
        self._shutdown_event.set()

        # Clear all timers
        self._active_timers.clear()

        # Signal workers to shutdown
        for _ in self._worker_threads:
            self._callback_queue.put((None, None))

        # Wait for threads to finish
        for worker in self._worker_threads:
            worker.join(timeout=1.0)

        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=1.0)


class EfficientTimer:
    """Lightweight timer object that tracks timing information."""

    def __init__(self, timer_id: str, callback: t.Callable, interval: float):
        self._timer_id = timer_id
        self._callback = callback
        self._interval = interval
        self._next_run = time.time() + interval

    def should_execute(self, current_time: float) -> bool:
        """Check if this timer should execute now."""
        return current_time >= self._next_run

    def update_next_run(self, current_time: float) -> None:
        """Update the next execution time."""
        self._next_run = current_time + self._interval

    def get_callback(self) -> t.Callable:
        """Get the callback function."""
        return self._callback

    def get_id(self) -> str:
        """Get the timer ID."""
        return self._timer_id


class Timer(BaseTimer):
    """ Efficient timer class for high-performance concurrent scheduling. """

    _timer_counter: int = 0
    _counter_lock = threading.Lock()

    def __init__(self, callback: t.Callable, time_period_ns: float, **options) -> None:
        super().__init__(callback=callback, time_period_ns=time_period_ns, **options)

        # Generate unique timer ID
        with Timer._counter_lock:
            Timer._timer_counter += 1
            self._timer_id = f"timer_{Timer._timer_counter}"

        self._interval = time_period_ns / 1000000.0  # Convert to seconds
        self._manager = EfficientTimerManager.get_instance()
        self._is_active = False

        # Schedule the timer
        self._schedule_timer()

    @property
    def interval(self) -> float:
        """ Returns the timer's interval. """
        return self._interval

    def _schedule_timer(self) -> None:
        """Schedule this timer with the manager."""
        try:
            self._manager.schedule_timer(self._timer_id, self._callback, self._interval)
            self._is_active = True
        except Exception as e:
            logger.error(f"Failed to schedule timer {self._timer_id}: {e}")

    @staticmethod
    def start_timer() -> None:
        """ Function for triggering the timer (compatibility method). """
        # Timers start automatically when created, this is for compatibility
        pass

    @staticmethod
    def shutdown_all_timers() -> None:
        """ Function for shutting down all timers. """
        try:
            manager = EfficientTimerManager.get_instance()
            manager.shutdown()
        except Exception as e:
            logger.error(f"Error shutting down timers: {e}")

    @staticmethod
    def get_active_job_count() -> int:
        """ Get the number of active timer jobs. """
        try:
            manager = EfficientTimerManager.get_instance()
            return manager.get_active_timer_count()
        except Exception:
            return 0

    @staticmethod
    def get_scheduler_info() -> t.Dict[str, t.Any]:
        """ Get scheduler information for debugging. """
        try:
            manager = EfficientTimerManager.get_instance()
            return {
                "status": "running",
                "active_jobs": manager.get_active_timer_count(),
                "type": "efficient_queue_based"
            }
        except Exception:
            return {"status": "error", "active_jobs": 0, "type": "efficient_queue_based"}

    def destroy(self, **options) -> None:
        """ Function for destroying this specific timer instance. """
        if self._is_active:
            try:
                self._manager.cancel_timer(self._timer_id)
                self._is_active = False
            except Exception as e:
                logger.error(f"Error destroying timer {self._timer_id}: {e}")

    def is_running(self) -> bool:
        """ Check if this timer is currently running. """
        return self._is_active


class Rate(BaseRate):
    """A utility for sleeping at a fixed rate using asyncio."""

    def __init__(self, timer: Timer, **options) -> None:
        super().__init__(timer=timer, **options)
        self._interval = timer.interval
        self._last_time = None

    def sleep(self, **options) -> None:
        """ Block until timer triggers. """
        import time
        current_time = time.time()

        if self._last_time is not None:
            elapsed = current_time - self._last_time
            sleep_time = max(0, self._interval - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)

        self._last_time = time.time()

    def destroy(self, **options) -> None:
        """ Function for destroying the rate object. """
        if hasattr(self, '_timer') and self._timer is not None:
            self._timer.destroy()