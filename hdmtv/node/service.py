#!/usr/bin/python
# -*- coding: utf-8 -*-

import socket
import msgpack
import typing as t
from threading import Thread, Lock
from concurrent.futures import ThreadPoolExecutor
from hdmtv.core.flags import Codes
from hdmtv.core.config import Config
from hdmtv.core.cdi import get_instance
from hdmtv.node.utils import ServiceParser
from hdmtv.core.node import BaseService, Response, Request


class _RPCServer(Thread):
    """ RPC server definition class. """

    def __init__(self, host: str = 'localhost', port: int = 9999) -> None:
        super().__init__()
        self.host = host
        self.port = port
        self.funcs: t.Dict[str, t.Callable] = {}
        self.running = False

        config: Config = get_instance(clazz=Config)
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        max_workers: int = config.get_dict(prefix="TIMER").get("max_workers", 8)
        self._executor: t.Optional[ThreadPoolExecutor] = None
        if max_workers > 0:
            self._executor: t.Optional[ThreadPoolExecutor] = ThreadPoolExecutor(max_workers=max_workers)

    def register_function(self, func: t.Callable, name: str = None) -> None:
        """注册可供远程调用的函数"""
        name = name or func.__name__
        self.funcs[name] = func

    def _handle_connection(self, client_socket: socket.socket) -> None:
        """处理客户端连接"""
        try:
            # 1. 读取数据头（4字节长度）
            header = client_socket.recv(4)
            if not header:
                return
            data_len = int.from_bytes(header, byteorder='big')

            # 2. 读取完整数据
            data = bytearray()
            while len(data) < data_len:
                remaining = data_len - len(data)
                chunk = client_socket.recv(min(4096, remaining))
                if not chunk:
                    raise ConnectionError("Incomplete data")
                data.extend(chunk)

            # 3. 反序列化并处理请求
            request = msgpack.unpackb(data, raw=False)
            func_name = request['func']
            args = request.get('args', [])
            # kwargs = request.get('kwargs', {})

            if len(args) == 0 or not isinstance(args[0], dict):
                response = Response(code=Codes.REQUEST_DATATYPE_INVALID, message="Invalid request datatype.")
            elif func_name not in self.funcs:
                response = Response(code=Codes.RESPONSE_FUNCTION_NOT_FOUND, message="Response function not found.")
            else:
                try:
                    request: Request = Request(**args[0])
                    response = self.funcs[func_name](request)
                except Exception as expt:
                    response = Response(code=Codes.RESPONSE_PROCESS_ERROR, message=str(expt))

            # 4. 发送响应
            response_data = msgpack.packb(response, use_bin_type=True)
            header = len(response_data).to_bytes(4, 'big')
            client_socket.sendall(header + response_data)

        except Exception as e:
            print(f"处理请求时发生错误: {str(e)}")
        finally:
            client_socket.close()

    def run(self) -> None:
        """启动RPC服务端"""
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(5)
        self.running = True
        # print(f"RPC Server 启动于 {self.host}:{self.port}")

        try:
            while self.running:
                client_socket, addr = self.server_socket.accept()
                if self._executor is not None:
                    self._executor.submit(self._handle_connection, client_socket)
                else:
                    thread = Thread(target=self._handle_connection, args=(client_socket,))
                    thread.start()
        except Exception as expt:
            self.stop()

    def stop(self) -> None:
        """停止服务端"""
        if not self.running:
            return
        try:
            self.server_socket.close()
        except Exception as expt:
            print(expt)
        self.running = False


class Service(BaseService):
    """ Service server class. """

    _server_matrix: t.Dict[str, t.Optional[_RPCServer]] = dict()
    _endpoint_ref_count: t.Dict[str, int] = dict()  # 新增：跟踪每个endpoint的引用计数
    _registered_functions: t.Dict[str, t.Set[str]] = dict()  # 新增：跟踪每个endpoint注册的函数
    _is_start: bool = False
    _is_destroyed: bool = False

    def __init__(self, srv_name: str, callback: t.Callable, **options) -> None:
        super().__init__(srv_name=srv_name, callback=callback, **options)
        self._parser: ServiceParser = ServiceParser(url=srv_name)
        self._thread_lock: Lock = Lock()
        self._endpoint: str = self._parser.endpoint
        self._func_name: str = self._parser.func_string
        self._callback = callback
        self._is_instance_destroyed: bool = False  # 实例级别的销毁标志

        # 初始化endpoint相关的数据结构
        if self._endpoint not in self._server_matrix:
            self._server_matrix[self._endpoint] = None
            self._endpoint_ref_count[self._endpoint] = 0
            self._registered_functions[self._endpoint] = set()

        # 增加引用计数
        self._endpoint_ref_count[self._endpoint] += 1

        # 记录注册的函数
        self._registered_functions[self._endpoint].add(self._func_name)

        # 检查并创建_RPCServer
        self._ensure_rpc_server()

    def _ensure_rpc_server(self) -> None:
        """ 确保当前endpoint的_RPCServer已创建并启动 """
        with self._thread_lock:
            # 检查是否需要创建_RPCServer
            if self._server_matrix.get(self._endpoint) is None:
                # 需要创建并启动_RPCServer
                self._init_single_rpc_server(self._endpoint)

            # 注册当前服务的函数
            server = self._server_matrix[self._endpoint]
            if server is not None:
                server.register_function(func=self._callback, name=self._func_name)

    def _init_single_rpc_server(self, endpoint: str) -> None:
        """ 初始化单个endpoint的RPC服务器 """
        if self._server_matrix.get(endpoint) is None:
            # 创建_RPCServer
            server = _RPCServer(host=self._parser.host, port=self._parser.port)
            self._server_matrix[endpoint] = server

            # 启动服务器
            server.start()

    def send_response(self, response: Response, **options) -> None:
        """ Function for sending the response. """
        raise NotImplementedError("This function has not been implemented.")

    def destroy(self, **options) -> None:
        """ Destroy the current service server. """
        if not self._is_instance_destroyed:
            with self._thread_lock:
                # 减少当前endpoint的引用计数
                if self._endpoint in self._endpoint_ref_count:
                    self._endpoint_ref_count[self._endpoint] -= 1

                    # 从注册函数集合中移除当前函数
                    if self._endpoint in self._registered_functions:
                        self._registered_functions[self._endpoint].discard(self._func_name)

                    # 如果引用计数为0，释放_RPCServer资源
                    if self._endpoint_ref_count[self._endpoint] <= 0:
                        if self._endpoint in self._server_matrix and self._server_matrix[self._endpoint] is not None:
                            server = self._server_matrix[self._endpoint]
                            if server.running:
                                server.stop()
                            self._server_matrix[self._endpoint] = None

                        # 清理相关资源
                        if self._endpoint in self._registered_functions:
                            self._registered_functions[self._endpoint].clear()

                        # 从引用计数中移除
                        if self._endpoint in self._endpoint_ref_count:
                            del self._endpoint_ref_count[self._endpoint]

            self._is_instance_destroyed = True


if __name__ == "__main__":
    from injector import singleton
    from hdmtv.core.cdi import binding
    config_file: str = r"D:\Gitlab\hdmtv\hdmtv\configs\config.yaml"
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)

    def callback_func(request) -> Response:
        print("Received request!, {}".format(request))
        resp = Response(code=0, message="Hey there", data={"a": 1})
        print(resp)
        return resp

    service: Service = Service(srv_name="/localhost/ns/say_hello", callback=callback_func)

    while True:
        inp = input(">>")
        if inp.lower() == "e":
            break
    service.destroy()
