#!/usr/bin/python
# -*- coding: utf-8 -*-

import asyncio
import typing as t
from asyncua import Client
from typing import TypeVar
from threading import Thread
from hdmtv.core.node import BaseSubscription
from hdmtv.node.utils import TopicUrlParser
from hdmtv.core.cdi import get_instance
from hdmtv.recorder import DataRecorder

MsgType = TypeVar('MsgType')


class SubscriptionHandler(object):
    """ Subscription handlr class. """

    def __init__(self, callback: t.Optional[t.Callable] = None, topic: str = "", **options) -> None:
        self._callback = callback
        self._options = options
        self._topic = topic  # 添加topic参数
        
        # 获取recorder实例
        try:
            self._recorder = get_instance(clazz=DataRecorder)
        except:
            self._recorder = None

    def datachange_notification(self, node, val, data) -> None:
        if self._callback is not None:
            self._callback(val)
            # 记录接收的消息
            if self._recorder:
                self._recorder.record(self._topic, val)


class _ClientRunner(Thread):
    """ OPCUA client runner class. """

    def __init__(
        self,
        endpoint: str,
        parsers: t.List[TopicUrlParser],
        handlers: t.List[SubscriptionHandler],
        **options
    ) -> None:
        super().__init__(**options)
        self._is_exit: bool = False
        self._endpoint = endpoint
        self._parsers = parsers
        self._handlers = handlers
        self._client: t.Optional[Client] = None
        self._cur_subscription: t.Any = None
        self._options = options

    @property
    def client(self) -> t.Optional[Client]:
        """ Returns the client object. """
        return self._client

    async def _resilient_loop(self) -> None:
        """ Main resilient loop. """
        while not self._is_exit:
            try:
                await self._setup_client()
                while not self._is_exit:
                    await asyncio.sleep(1.0)  # 健康检查间隔
                    await self.client.check_connection()
            except Exception as expt:
                await self._cleanup()
                await asyncio.sleep(1.0)

    async def _setup_client(self) -> None:
        """ Main function for the client's setup. """
        client = Client(url=self._endpoint)
        await client.connect()

        for parser, handler in zip(self._parsers, self._handlers):
            ns_idx: int = await client.get_namespace_index(parser.namespace)
            node = client.get_node(parser.get_node_id(ns_idx=ns_idx))

            # 创建新订阅
            self._cur_subscription = await client.create_subscription(
                period=10,  # 发布间隔
                handler=handler
            )
            await self._cur_subscription.subscribe_data_change(node)
        self._client = client

    async def _cleanup(self) -> None:
        """清理残留资源"""
        try:
            if self._cur_subscription:
                await self._cur_subscription.delete()
                self._cur_subscription = None
            if self._client:
                await self._client.disconnect()
                self._client = None
        except Exception as expt:
            print(expt)

    def exit(self) -> None:
        """ Function for exiting the running loop. """
        self._is_exit = True

    def run(self) -> None:
        """ Main running loop. """
        asyncio.run(self._resilient_loop())


class Subscription(BaseSubscription):
    """ Subscriber defining the subscribing used by node. """

    _runner_matrix: t.Dict[str, t.Optional[_ClientRunner]] = dict()
    _parsers_matrix: t.Dict[str, t.List[TopicUrlParser]] = dict()
    _handlers_matrix: t.Dict[str, t.List[SubscriptionHandler]] = dict()
    _endpoint_ref_count: t.Dict[str, int] = dict()  # 新增：跟踪每个endpoint的引用计数
    _is_start: bool = False
    _is_destroyed: bool = False

    def __init__(
        self,
        topic: str,
        callback: t.Optional[t.Callable] = None,
        msg_type: t.Optional[MsgType] = None,
        **options
    ) -> None:
        super().__init__(topic=topic, msg_type=msg_type, **options)
        parser: TopicUrlParser = TopicUrlParser(url=topic)
        handler: SubscriptionHandler = SubscriptionHandler(callback=callback, topic=topic)  # 传递topic
        endpoint: str = parser.endpoint
        self._parser = parser
        self._handler = handler
        self._endpoint = endpoint
        self._is_instance_destroyed: bool = False  # 实例级别的销毁标志

        # 初始化endpoint相关的数据结构
        if endpoint not in self._runner_matrix:
            self._runner_matrix[endpoint] = None
            self._parsers_matrix[endpoint] = list()
            self._handlers_matrix[endpoint] = list()
            self._endpoint_ref_count[endpoint] = 0

        # 增加引用计数
        self._endpoint_ref_count[endpoint] += 1

        # 添加到对应的列表中
        self._parsers_matrix[endpoint].append(parser)
        self._handlers_matrix[endpoint].append(handler)

        # 检查并创建_ClientRunner
        self._ensure_client_runner()

    def _ensure_client_runner(self) -> None:
        """ 确保当前endpoint的_ClientRunner已创建并启动 """
        # 检查是否需要创建_ClientRunner
        if self._runner_matrix.get(self._endpoint) is None:
            # 需要创建并启动_ClientRunner
            self._init_single_client_runner(self._endpoint)

    def _init_single_client_runner(self, endpoint: str) -> None:
        """ 初始化单个endpoint的客户端运行器 """
        if self._runner_matrix.get(endpoint) is None:
            # 标准化endpoint格式

            # 获取当前endpoint的parsers和handlers
            parsers: t.List[TopicUrlParser] = self._parsers_matrix[endpoint]
            handlers: t.List[SubscriptionHandler] = self._handlers_matrix[endpoint]

            # 创建并启动_ClientRunner
            client_runner: _ClientRunner = _ClientRunner(
                endpoint=self._parser.endpoint,
                parsers=parsers,
                handlers=handlers
            )
            self._runner_matrix[endpoint] = client_runner
            client_runner.start()

    def destroy(self, **options) -> None:
        """ Function for destroying the subscription. """
        if not self._is_instance_destroyed:
            # 减少当前endpoint的引用计数
            if self._endpoint in self._endpoint_ref_count:
                self._endpoint_ref_count[self._endpoint] -= 1

                # 从parsers和handlers列表中移除当前实例
                if self._endpoint in self._parsers_matrix and self._parser in self._parsers_matrix[self._endpoint]:
                    self._parsers_matrix[self._endpoint].remove(self._parser)
                if self._endpoint in self._handlers_matrix and self._handler in self._handlers_matrix[self._endpoint]:
                    self._handlers_matrix[self._endpoint].remove(self._handler)

                # 如果引用计数为0，释放_ClientRunner资源
                if self._endpoint_ref_count[self._endpoint] <= 0:
                    if self._endpoint in self._runner_matrix and self._runner_matrix[self._endpoint] is not None:
                        self._runner_matrix[self._endpoint].exit()
                        self._runner_matrix[self._endpoint] = None

                    # 清理相关资源
                    if self._endpoint in self._parsers_matrix:
                        self._parsers_matrix[self._endpoint] = []
                    if self._endpoint in self._handlers_matrix:
                        self._handlers_matrix[self._endpoint] = []

                    # 从引用计数中移除
                    if self._endpoint in self._endpoint_ref_count:
                        del self._endpoint_ref_count[self._endpoint]

            self._is_instance_destroyed = True


if __name__ == "__main__":
    from hdmtv.core.config import Config
    from injector import singleton
    from hdmtv.core.cdi import binding
    config_file: str = r"D:\Gitlab\hdmtv\hdmtv\configs\config.yaml"
    binding(interface=Config, to=Config(config_file=config_file), scope=singleton)

    def callback(val: t.Any) -> None:
        print("Received callback value: {}".format(val))

    subscriber1: Subscription = Subscription(topic="/localhost/ns1/robot/j1", callback=callback)
    subscriber2: Subscription = Subscription(topic="/localhost/ns1/robot/axis_name", callback=callback)

    while True:
        value = input("\n>>")
        if value.lower() == "e":
            break
    subscriber1.destroy()
    subscriber2.destroy()