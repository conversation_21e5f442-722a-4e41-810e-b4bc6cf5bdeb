#!/usr/bin/python
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages
from hdmtv.version import VERSION_TAG

# with open("README.md") as f:
#     readme = f.read()

# with open("LICENSE") as f:
#     license = ''  # f.read()

with open("requirements.txt") as f:
    requirements = f.read()
    f.close()

setup(
    name="hdmtv",
    version=VERSION_TAG,
    description="MTV software structure",
    long_description='',
    author="Shuai",
    author_email="<EMAIL>",
    url="https://github.com/shuaih7/hdmtv",
    license='license',
    classifiers=["Programming Language :: Python :: 3.10.11",
                 "Programming Language :: Python :: Implementation :: CPython", ],
    packages=find_packages(exclude="docs"),
    include_package_data=True,
    python_requires=">=3.10",
    install_requires=requirements,
    package_data={},
    test_suits="nose.collector",
    tests_require=["nose"],
)
